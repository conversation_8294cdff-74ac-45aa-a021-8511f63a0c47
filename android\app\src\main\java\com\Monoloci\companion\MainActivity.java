package com.Monoloci.companion;

import com.getcapacitor.BridgeActivity;
import com.google.firebase.FirebaseApp;
import android.os.Bundle;

public class MainActivity extends BridgeActivity {
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize Firebase
        if (FirebaseApp.getApps(this).isEmpty()) {
            FirebaseApp.initializeApp(this);
        }
        
        // Firebase is initialized above and used through web SDK
        // No need to register native Firebase plugins since we're using web Firebase
    }
}
