/**
 * Secure Error Handler for Production
 * Prevents sensitive information disclosure in error logs
 */

class SecureErrorHandler {
    constructor() {
        this.isProduction = window.APP_CONFIG?.environment === 'production';
        this.errorCounts = new Map();
        this.maxErrorsPerType = 10; // Prevent error spam
    }

    /**
     * Handle errors securely without exposing sensitive information
     */
    handleError(error, context = 'general', userMessage = null) {
        const errorType = this.getErrorType(error);
        
        // Rate limit error logging
        if (this.shouldLogError(errorType)) {
            this.logSecureError(error, context);
        }

        // Return user-friendly message
        return this.getUserMessage(errorType, userMessage);
    }

    /**
     * Determine error type for categorization
     */
    getErrorType(error) {
        if (!error) return 'unknown';
        
        const message = error.message || error.toString();
        
        if (message.includes('auth')) return 'authentication';
        if (message.includes('network') || message.includes('fetch')) return 'network';
        if (message.includes('permission') || message.includes('denied')) return 'permission';
        if (message.includes('validation') || message.includes('invalid')) return 'validation';
        if (message.includes('firebase') || message.includes('firestore')) return 'firebase';
        if (message.includes('maps') || message.includes('google')) return 'maps';
        
        return 'application';
    }

    /**
     * Check if we should log this error (rate limiting)
     */
    shouldLogError(errorType) {
        const count = this.errorCounts.get(errorType) || 0;
        if (count >= this.maxErrorsPerType) {
            return false;
        }
        
        this.errorCounts.set(errorType, count + 1);
        return true;
    }

    /**
     * Log error securely (sanitized for production)
     */
    logSecureError(error, context) {
        const timestamp = new Date().toISOString();
        const errorType = this.getErrorType(error);
        
        if (this.isProduction) {
            // Production: Log minimal, sanitized information
            console.error(`[${timestamp}] ${errorType.toUpperCase()} in ${context}`);
            
            // Log to external service if available
            if (window.SecurityMonitor) {
                window.SecurityMonitor.logError({
                    type: errorType,
                    context: context,
                    timestamp: timestamp,
                    userAgent: navigator.userAgent.substring(0, 100), // Truncated
                    url: window.location.pathname // No query params
                });
            }
        } else {
            // Development: Log full error details
            console.error(`[${timestamp}] Error in ${context}:`, error);
        }
    }

    /**
     * Get user-friendly error message
     */
    getUserMessage(errorType, customMessage) {
        if (customMessage) return customMessage;

        const messages = {
            authentication: 'Authentication failed. Please sign in again.',
            network: 'Network error. Please check your connection and try again.',
            permission: 'Permission denied. Please check your account permissions.',
            validation: 'Invalid input. Please check your data and try again.',
            firebase: 'Service temporarily unavailable. Please try again later.',
            maps: 'Map service unavailable. Please try again later.',
            application: 'An unexpected error occurred. Please try again.',
            unknown: 'Something went wrong. Please try again.'
        };

        return messages[errorType] || messages.unknown;
    }

    /**
     * Handle async errors with proper error boundaries
     */
    async handleAsync(asyncFunction, context = 'async', fallbackValue = null) {
        try {
            return await asyncFunction();
        } catch (error) {
            const userMessage = this.handleError(error, context);
            
            // Show user-friendly message
            if (window.showUserMessage) {
                window.showUserMessage(userMessage, 'error');
            }
            
            return fallbackValue;
        }
    }

    /**
     * Wrap functions with error handling
     */
    wrapFunction(fn, context = 'function') {
        return (...args) => {
            try {
                const result = fn.apply(this, args);
                
                // Handle promises
                if (result && typeof result.catch === 'function') {
                    return result.catch(error => {
                        this.handleError(error, context);
                        throw error; // Re-throw for caller to handle
                    });
                }
                
                return result;
            } catch (error) {
                this.handleError(error, context);
                throw error; // Re-throw for caller to handle
            }
        };
    }

    /**
     * Clear error counts (call periodically)
     */
    resetErrorCounts() {
        this.errorCounts.clear();
    }

    /**
     * Get error statistics (for monitoring)
     */
    getErrorStats() {
        const stats = {};
        for (const [type, count] of this.errorCounts.entries()) {
            stats[type] = count;
        }
        return stats;
    }
}

// Create global instance
window.SecureErrorHandler = new SecureErrorHandler();

// Reset error counts every hour
setInterval(() => {
    window.SecureErrorHandler.resetErrorCounts();
}, 3600000);

// Global error handler
window.addEventListener('error', (event) => {
    window.SecureErrorHandler.handleError(event.error, 'global');
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
    window.SecureErrorHandler.handleError(event.reason, 'promise');
    event.preventDefault(); // Prevent default browser error logging
});

console.log('🛡️ Secure Error Handler initialized');

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecureErrorHandler;
}
