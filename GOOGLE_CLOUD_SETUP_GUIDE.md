# 🔐 Google Cloud Console API Key Security Setup

## Your API Keys to Secure:
- **Firebase API Key:** `AIzaSyBA0X9xdiOL3nNdqzKibPIZVSciG88iR6Q`
- **Google Maps API Key:** `AIzaSyDwwiFkI0LfJaYPuixc8fKo53pJITUQpMs`

---

## 🔥 Firebase API Key Setup

### Step 1: Access Google Cloud Console
1. Go to: https://console.cloud.google.com/apis/credentials
2. Select project: **aura-app-backend**
3. Find API key: `AIzaSyBA0X9xdiOL3nNdqzKibPIZVSciG88iR6Q`
4. Click the **pencil icon** to edit

### Step 2: Set Application Restrictions
```
Application restrictions: HTTP referrers (web sites)

Website restrictions (add these):
✅ https://aura-app-backend.web.app/*
✅ https://aura-app-backend.firebaseapp.com/*
✅ https://aura-app-staging-999.web.app/*
✅ https://monoloci.xyz/*
✅ https://www.monoloci.xyz/*
✅ https://app.monoloci.xyz/*
✅ https://monoloci.app/*

For development (remove before production):
✅ http://localhost:*
✅ http://127.0.0.1:*
```

### Step 3: Set API Restrictions
```
API restrictions: Restrict key

Select these APIs:
✅ Cloud Firestore API
✅ Firebase Authentication API  
✅ Firebase Storage API
✅ Identity and Access Management (IAM) API
✅ Firebase Installations API
✅ Token Service API
```

### Step 4: Save Changes
- Click **SAVE**
- Wait 5-10 minutes for changes to propagate

---

## 🗺️ Google Maps API Key Setup

### Step 1: Find Maps API Key
1. In the same credentials page
2. Find API key: `AIzaSyDwwiFkI0LfJaYPuixc8fKo53pJITUQpMs`
3. Click the **pencil icon** to edit

### Step 2: Set Application Restrictions
```
Application restrictions: HTTP referrers (web sites)

Website restrictions (add these):
✅ https://aura-app-backend.web.app/*
✅ https://aura-app-backend.firebaseapp.com/*
✅ https://aura-app-staging-999.web.app/*
✅ https://monoloci.xyz/*
✅ https://www.monoloci.xyz/*
✅ https://app.monoloci.xyz/*
✅ https://monoloci.app/*

For development (remove before production):
✅ http://localhost:*
✅ http://127.0.0.1:*
```

### Step 3: Set API Restrictions
```
API restrictions: Restrict key

Select these APIs:
✅ Maps JavaScript API
✅ Geocoding API
✅ Places API (if you use place search)
✅ Geolocation API (if you use geolocation)
```

### Step 4: Save Changes
- Click **SAVE**
- Wait 5-10 minutes for changes to propagate

---

## 🔒 Additional Security Settings

### Enable API Usage Monitoring
1. Go to: https://console.cloud.google.com/apis/dashboard
2. Click on each API you're using
3. Set up **quotas and limits**:
   - Maps JavaScript API: 25,000 requests/day
   - Geocoding API: 2,500 requests/day
   - Firebase APIs: Monitor usage

### Set Up Billing Alerts
1. Go to: https://console.cloud.google.com/billing
2. Click **Budgets & alerts**
3. Create budget with alerts at:
   - 50% of expected usage
   - 90% of expected usage
   - 100% of expected usage

### Enable Audit Logging
1. Go to: https://console.cloud.google.com/iam-admin/audit
2. Enable audit logs for:
   - Admin Activity
   - Data Access (for sensitive operations)

---

## ✅ Verification Steps

### Test Firebase Connection
1. Open your app in browser
2. Check browser console for errors
3. Verify Firebase authentication works
4. Test Firestore read/write operations

### Test Google Maps
1. Navigate to map features in your app
2. Verify maps load correctly
3. Check for API key errors in console

### Monitor API Usage
1. Check: https://console.cloud.google.com/apis/dashboard
2. Verify requests are being logged
3. Ensure no unauthorized usage

---

## 🚨 Important Notes

### Domain Configuration
- Replace `yourdomain.com` with your actual production domain
- Remove localhost restrictions before production deployment
- Add staging domains if you have a staging environment

### API Key Rotation
- Consider rotating API keys every 6 months
- Update your `.env` file when rotating keys
- Test thoroughly after key rotation

### Emergency Access
- Keep a backup admin account with access to Google Cloud Console
- Document the API key restriction process for your team
- Have a rollback plan if restrictions cause issues

---

## 📞 Support

If you encounter issues:
1. Check the Google Cloud Console error logs
2. Verify domain restrictions match your deployment URL exactly
3. Ensure API restrictions include all necessary services
4. Wait 10 minutes after changes before testing

**Next Step:** After completing this setup, proceed to Step 3 (Deploy Security Headers)
