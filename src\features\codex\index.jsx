import React from 'react';
import ReactDOM from 'react-dom/client';
import Codex from './Codex';

const container = document.getElementById('codex-root');
if (container) {
    const root = ReactDOM.createRoot(container);
    root.render(
        <React.StrictMode>
            <Codex />
        </React.StrictMode>
    );
} else {
    console.error('Failed to find the root element for the Codex feature.');
}