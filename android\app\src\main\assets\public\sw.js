const CACHE_NAME = 'Monoloci-app-v1.3.0';
const urlsToCache = [
  '/',
  '/index.html',
  '/index-fallback.html',
  '/app.js',
  '/journey.html',
  '/journey.js',
  '/codex.js',
  '/style.css',
  '/solo.css',
  '/manifest.json',
  '/icons/icon-192.png',
  '/icons/icon-512.png'
];

// HTTPS Enforcement - Redirect HTTP to HTTPS
self.addEventListener('fetch', (event) => {
  // Only enforce HTTPS in production (not for localhost development)
  if (event.request.url.startsWith('http://') &&
      !event.request.url.includes('localhost') &&
      !event.request.url.includes('127.0.0.1')) {

    const httpsUrl = event.request.url.replace('http://', 'https://');
    event.respondWith(Response.redirect(httpsUrl, 301));
    return;
  }

  // Continue with normal fetch handling
  handleFetch(event);
});

function handleFetch(event) {
  const { request } = event;
  const url = new URL(request.url);

  // Ignore non-GET requests
  if (request.method !== 'GET') {
      return;
  }

  // Strategy 1: Bypass Google APIs and other third-party scripts entirely
  if (url.hostname !== self.location.hostname || url.protocol !== 'https:') {
      // Exception for tailwindcss cdn
      if (url.hostname === 'cdn.tailwindcss.com') {
           // Use a cache-first strategy for tailwind
          event.respondWith(
              caches.match(request).then(cachedResponse => {
                  return cachedResponse || fetch(request).then(networkResponse => {
                      const responseToCache = networkResponse.clone();
                      caches.open(CACHE_NAME).then(cache => {
                          cache.put(request, responseToCache);
                      });
                      return networkResponse;
                  });
              })
          );
      } else {
          return; // Let the browser handle it
      }
      return;
  }

  // Strategy 2: Cache-first for core app assets
  if (urlsToCache.includes(url.pathname) || url.pathname.endsWith('.css') || url.pathname.endsWith('.js')) {
      event.respondWith(
          caches.match(request).then(cachedResponse => {
              if (cachedResponse) {
                  return cachedResponse;
              }
              return fetch(request).then(networkResponse => {
                  const responseToCache = networkResponse.clone();
                  caches.open(CACHE_NAME).then(cache => {
                      cache.put(request, responseToCache);
                  });
                  return networkResponse;
              });
          })
      );
      return;
  }

  // Strategy 3: Network-first for dynamic content
  event.respondWith(
      fetch(request).then(networkResponse => {
          // Cache successful responses
          if (networkResponse.status === 200) {
              const responseToCache = networkResponse.clone();
              caches.open(CACHE_NAME).then(cache => {
                  cache.put(request, responseToCache);
              });
          }
          return networkResponse;
      }).catch(() => {
          // Fallback to cache if network fails
          return caches.match(request).then(cachedResponse => {
              return cachedResponse || new Response('Offline content not available', {
                  status: 503,
                  statusText: 'Service Unavailable'
              });
          });
      })
  );
}

// Install event - cache resources
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching core assets');
        const cachePromises = urlsToCache.map(url => {
            return cache.add(url).catch(err => {
                console.warn(`Service Worker: Failed to cache ${url}`, err);
            });
        });
        return Promise.all(cachePromises);
      })
      .then(() => {
        console.log('Service Worker: Installation complete, skipping waiting');
        return self.skipWaiting(); // Activate immediately
      })
      .catch(error => {
        console.error('Service Worker: Installation failed', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      // Clean up any accidentally cached Google Maps responses
      return caches.open(CACHE_NAME).then(cache => {
        return cache.keys().then(requests => {
          const googleRequests = requests.filter(request => {
            const url = new URL(request.url);
            return url.hostname.includes('googleapis.com') ||
                   url.hostname.includes('gstatic.com') ||
                   url.hostname.includes('google.com') ||
                   url.pathname.includes('/maps/') ||
                   url.search.includes('maps');
          });

          return Promise.all(
            googleRequests.map(request => {
              console.log('Service Worker: Removing cached Google/Maps request:', request.url);
              return cache.delete(request);
            })
          );
        });
      });
    }).then(() => {
      console.log('Service Worker: Activation complete');
      return self.clients.claim();
    })
  );
});

// Note: Fetch event handling is now consolidated in the handleFetch function above


// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'emergency-sync') {
    event.waitUntil(syncEmergencyData());
  }
});

// Push notifications for emergency alerts
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'Emergency alert received',
    icon: '/icons/icon-192.png',
    badge: '/icons/icon-192.png',
    vibrate: [200, 100, 200],
    tag: 'emergency-alert',
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'View Details',
        icon: '/icons/icon-192.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/icon-192.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('Monoloci Emergency Alert', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked', event.action);
  
  event.notification.close();

  if (event.action === 'view') {
    // Open the app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Sync emergency data when back online
async function syncEmergencyData() {
  try {
    // Get stored emergency data from IndexedDB
    const emergencyData = await getStoredEmergencyData();
    
    if (emergencyData && emergencyData.length > 0) {
      // Send to server when back online
      for (const data of emergencyData) {
        await fetch('/api/emergency', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data)
        });
      }
      
      // Clear stored data after successful sync
      await clearStoredEmergencyData();
      console.log('Service Worker: Emergency data synced successfully');
    }
  } catch (error) {
    console.error('Service Worker: Emergency data sync failed', error);
  }
}

// Helper functions for IndexedDB operations
async function getStoredEmergencyData() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('MonolociEmergencyDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction(['emergencyData'], 'readonly');
      const store = transaction.objectStore('emergencyData');
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = () => resolve(getAllRequest.result);
      getAllRequest.onerror = () => reject(getAllRequest.error);
    };
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('emergencyData')) {
        db.createObjectStore('emergencyData', { keyPath: 'id', autoIncrement: true });
      }
    };
  });
}

async function clearStoredEmergencyData() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('MonolociEmergencyDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction(['emergencyData'], 'readwrite');
      const store = transaction.objectStore('emergencyData');
      const clearRequest = store.clear();
      
      clearRequest.onsuccess = () => resolve();
      clearRequest.onerror = () => reject(clearRequest.error);
    };
  });
}

// Message handling for communication with main app
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_EMERGENCY_DATA') {
    // Store emergency data for offline sync
    storeEmergencyData(event.data.payload);
  }
});

async function storeEmergencyData(data) {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('MonolociEmergencyDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction(['emergencyData'], 'readwrite');
      const store = transaction.objectStore('emergencyData');
      const addRequest = store.add({
        ...data,
        timestamp: Date.now()
      });
      
      addRequest.onsuccess = () => resolve();
      addRequest.onerror = () => reject(addRequest.error);
    };
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('emergencyData')) {
        db.createObjectStore('emergencyData', { keyPath: 'id', autoIncrement: true });
      }
    };
  });
}
