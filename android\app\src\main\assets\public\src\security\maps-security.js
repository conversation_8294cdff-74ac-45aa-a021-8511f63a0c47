/**
 * Google Maps API Security Module
 * Provides client-side security enhancements for Google Maps API usage
 */

class MapsSecurityManager {
    constructor() {
        this.requestCount = 0;
        this.requestWindow = 60000; // 1 minute window
        this.maxRequestsPerWindow = 50; // Max requests per minute
        this.requestTimes = [];
        this.allowedDomains = this.getAllowedDomains();
        this.isInitialized = false;
        
        // Initialize monitoring
        this.initializeMonitoring();
    }

    /**
     * Get allowed domains - unified for all platforms
     */
    getAllowedDomains() {
        return [
            'localhost',
            '127.0.0.1',
            'localhost:8000',
            '127.0.0.1:8000',
            'monoloci.xyz',
            'www.monoloci.xyz',
            'app.monoloci.xyz',
            'monoloci.app',
            'aura-app-staging-999.web.app'
        ];
    }

    /**
     * Validate current domain against allowed domains
     */
    validateDomain() {
        const currentDomain = window.location.hostname;
        const isAllowed = this.allowedDomains.some(domain => {
            // Handle port numbers
            const domainWithoutPort = domain.split(':')[0];
            return currentDomain === domainWithoutPort || currentDomain === domain;
        });

        if (!isAllowed) {
            console.error('🚨 Security Alert: Unauthorized domain detected:', currentDomain);
            console.error('Allowed domains:', this.allowedDomains);
            return false;
        }

        return true;
    }

    /**
     * Rate limiting for API requests
     */
    checkRateLimit() {
        const now = Date.now();
        
        // Remove old requests outside the window
        this.requestTimes = this.requestTimes.filter(time => now - time < this.requestWindow);
        
        // Check if we're over the limit
        if (this.requestTimes.length >= this.maxRequestsPerWindow) {
            console.warn('🚨 Rate limit exceeded. Please slow down API requests.');
            return false;
        }

        // Add current request
        this.requestTimes.push(now);
        this.requestCount++;
        
        return true;
    }

    /**
     * Validate API key format and basic security
     */
    validateApiKey(apiKey) {
        if (!apiKey || typeof apiKey !== 'string') {
            console.error('🚨 Invalid API key format');
            return false;
        }

        // Check for placeholder values
        if (apiKey.includes('your-') || apiKey === 'your-google-maps-api-key-here') {
            console.error('🚨 Placeholder API key detected. Please configure your actual API key.');
            return false;
        }

        // Basic format validation for Google API keys
        if (!apiKey.startsWith('AIza') || apiKey.length < 35) {
            console.warn('⚠️ API key format appears invalid');
            return false;
        }

        return true;
    }

    /**
     * Secure API key loading with validation
     */
    getSecureApiKey() {
        const apiKey = window.GOOGLE_MAPS_CONFIG?.apiKey;
        
        if (!this.validateDomain()) {
            throw new Error('Domain validation failed');
        }

        if (!this.validateApiKey(apiKey)) {
            throw new Error('API key validation failed');
        }

        if (!this.checkRateLimit()) {
            throw new Error('Rate limit exceeded');
        }

        return apiKey;
    }

    /**
     * Initialize monitoring and logging
     */
    initializeMonitoring() {
        // Log security events
        this.logSecurityEvent('Maps Security Manager initialized', {
            domain: window.location.hostname,
            timestamp: new Date().toISOString()
        });

        // Monitor for suspicious activity (disabled to prevent conflicts)
        // this.setupSuspiciousActivityDetection();
    }

    /**
     * Setup detection for suspicious activity
     */
    setupSuspiciousActivityDetection() {
        // Monitor for rapid successive requests
        let rapidRequestCount = 0;
        const rapidRequestThreshold = 10;
        const rapidRequestWindow = 5000; // 5 seconds

        // Use a safer approach that doesn't override global fetch
        // Instead, monitor script loading for Maps API
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(this, tagName);

            if (tagName.toLowerCase() === 'script') {
                const originalSetSrc = Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype, 'src').set;
                Object.defineProperty(element, 'src', {
                    set: function(value) {
                        // Check if it's a Google Maps API request
                        if (typeof value === 'string' && value.includes('googleapis.com/maps')) {
                            rapidRequestCount++;

                            setTimeout(() => {
                                rapidRequestCount--;
                            }, rapidRequestWindow);

                            if (rapidRequestCount > rapidRequestThreshold) {
                                window.MapsSecurityManager?.logSecurityEvent('Suspicious rapid Maps API requests detected', {
                                    url: value.substring(0, 100), // Truncate for security
                                    count: rapidRequestCount,
                                    timestamp: new Date().toISOString()
                                });
                            }
                        }

                        return originalSetSrc.call(this, value);
                    },
                    get: Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype, 'src').get
                });
            }

            return element;
        };
    }

    /**
     * Log security events
     */
    logSecurityEvent(event, details = {}) {
        const logEntry = {
            event: event,
            timestamp: new Date().toISOString(),
            domain: window.location.hostname,
            userAgent: navigator.userAgent.substring(0, 100), // Truncated for privacy
            ...details
        };

        // Log to console in development
        if (window.APP_CONFIG?.environment === 'development') {
            console.log('🔒 Security Event:', logEntry);
        }

        // Store in localStorage for monitoring (keep last 50 events)
        try {
            const events = JSON.parse(localStorage.getItem('mapsSecurityEvents') || '[]');
            events.push(logEntry);
            
            // Keep only last 50 events
            if (events.length > 50) {
                events.splice(0, events.length - 50);
            }
            
            localStorage.setItem('mapsSecurityEvents', JSON.stringify(events));
        } catch (error) {
            console.warn('Could not store security event:', error);
        }
    }

    /**
     * Get security statistics
     */
    getSecurityStats() {
        return {
            totalRequests: this.requestCount,
            currentRateLimit: this.requestTimes.length,
            maxRateLimit: this.maxRequestsPerWindow,
            allowedDomains: this.allowedDomains,
            currentDomain: window.location.hostname,
            environment: window.APP_CONFIG?.environment
        };
    }

    /**
     * Reset rate limiting (for testing or manual reset)
     */
    resetRateLimit() {
        this.requestTimes = [];
        this.logSecurityEvent('Rate limit manually reset');
    }
}

// Create global instance
window.MapsSecurityManager = new MapsSecurityManager();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MapsSecurityManager;
}
