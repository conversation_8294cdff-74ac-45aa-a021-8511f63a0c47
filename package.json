{"name": "monoloci-activity-companion", "version": "1.0.0", "description": "Monoloci: Your Activity Companion - A safety-focused mobile app for activity participants", "main": "index.html", "scripts": {"dev": "python start-server.py", "build:css": "npx postcss src/input.css --config postcss.prod.config.js -o dist/tailwind.css", "build:css-dev": "npx postcss src/input.css -o dist/tailwind.css", "build:css-prod": "copy src\\style.css dist\\style.css && copy src\\solo.css dist\\solo.css && npm run build:css", "build:codex": "esbuild src/features/codex/index.jsx --bundle --outfile=dist/codex.js --jsx-factory=React.createElement --jsx-fragment=React.Fragment --format=iife --global-name=CodexApp", "build": "node config.js && npm run build:css && npm run build:codex && esbuild app.js --bundle --outfile=dist/app.js --jsx-factory=React.createElement --jsx-fragment=React.Fragment && node build.js && node fix-paths.js", "fix-paths": "node fix-paths.js", "deploy": "npm run build && echo 'Build complete! Deploy the dist/ folder to your hosting service'", "serve": "python -m http.server 8000", "serve:https": "http-server -p 8443 -S -C cert.pem -K key.pem", "test": "echo 'Open firebase-test.html to test Firebase connectivity'", "security:audit": "npm audit", "security:fix": "npm audit fix", "security:test": "node security-test-suite.js", "security:check": "npm run security:audit && npm run security:test", "cap:build": "npm run build && npx cap copy", "cap:sync": "npx cap sync", "cap:open:android": "npx cap open android", "cap:run:android": "npx cap run android", "android:dev": "npm run cap:build && npm run cap:open:android"}, "keywords": ["activity", "safety", "mobile", "pwa", "firebase", "emergency"], "author": "Monoloci Development Team", "license": "GPL-2.0", "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.4.2", "@capacitor/clipboard": "^7.0.1", "@capacitor/core": "^7.4.2", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.1.3", "@capacitor/geolocation": "^7.1.4", "@capacitor/push-notifications": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@capawesome/capacitor-file-picker": "^7.2.0", "firebase": "^12.0.0", "framer-motion": "^11.2.12", "react": "^19.1.0", "react-dom": "^19.1.0", "tone": "^15.0.4"}, "devDependencies": {"@eslint/js": "^9.31.0", "@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.19", "clean-css": "^5.3.2", "cssnano": "^7.0.1", "esbuild": "^0.25.6", "eslint": "^9.31.0", "eslint-plugin-security": "^3.0.1", "firebase-tools": "^14.11.1", "http-server": "^14.1.1", "https-localhost": "^4.7.1", "postcss-cli": "^11.0.0", "postcss-import": "^16.1.0", "tailwindcss": "^4.1.11", "terser": "^5.19.4", "typescript": "^5.8.3"}, "repository": {"type": "git", "url": "https://github.com/yourusername/Monoloci-app.git"}, "homepage": "https://Monoloci-app-backend.web.app/", "engines": {"node": ">=14.0.0"}}