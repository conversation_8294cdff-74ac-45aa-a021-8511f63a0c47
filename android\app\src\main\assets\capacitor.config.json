{"appId": "com.Monoloci.companion", "appName": "<PERSON><PERSON><PERSON>", "webDir": "dist", "plugins": {"SplashScreen": {"launchShowDuration": 2000, "launchAutoHide": true, "backgroundColor": "#0f172a", "androidSplashResourceName": "splash", "androidScaleType": "CENTER_CROP", "showSpinner": false, "androidSpinnerStyle": "large", "iosSpinnerStyle": "small", "spinnerColor": "#0ea5e9", "splashFullScreen": true, "splashImmersive": true, "layoutName": "launch_screen", "useDialog": true}, "StatusBar": {"style": "DARK", "backgroundColor": "#0ea5e9"}, "Geolocation": {"permissions": {"location": "always"}}, "Camera": {"permissions": {"camera": "always", "photos": "always"}}, "LocalNotifications": {"smallIcon": "ic_stat_icon_config_sample", "iconColor": "#0ea5e9", "sound": "beep.wav"}, "PushNotifications": {"presentationOptions": ["badge", "sound", "alert"]}, "App": {}}, "server": {"androidScheme": "https", "hostname": "localhost", "allowNavigation": ["https://monoloci-app-backend.web.app", "https://firebasestorage.googleapis.com", "https://maps.googleapis.com", "https://*.firebaseapp.com", "https://localhost"]}, "android": {"allowMixedContent": true, "captureInput": true, "webContentsDebuggingEnabled": true, "appendUserAgent": "MonolociApp", "overrideUserAgent": "MonolociApp/1.0", "backgroundColor": "#0f172a"}, "ios": {"contentInset": "automatic", "scrollEnabled": true}}