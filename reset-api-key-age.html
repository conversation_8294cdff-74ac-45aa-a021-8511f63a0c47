<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset API Key Age</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 20px;
            background: #f8fafc;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
        }
        .btn:hover {
            background: #2563eb;
        }
        .btn-success {
            background: #10b981;
        }
        .btn-success:hover {
            background: #059669;
        }
        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 12px 0;
        }
        .success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Reset API Key Age</h1>
        <p>This tool resets the API key creation date to mark it as "fresh" and stop rotation warnings.</p>

        <div id="current-status" class="status info">
            <strong>Current Status:</strong> Loading...
        </div>

        <div>
            <button class="btn" onclick="checkCurrentAge()">Check Current Age</button>
            <button class="btn btn-success" onclick="resetKeyAge()">Reset Key Age to Today</button>
            <button class="btn" onclick="clearAllData()">Clear All Rotation Data</button>
        </div>

        <div id="result"></div>

        <h3>What this does:</h3>
        <ul>
            <li><strong>Reset Key Age:</strong> Sets the API key creation date to today</li>
            <li><strong>Clear All Data:</strong> Removes all rotation tracking data</li>
            <li><strong>Check Age:</strong> Shows current key age without changing anything</li>
        </ul>

        <p><strong>Note:</strong> This only affects the local rotation tracking. Your actual API key in Google Cloud Console is unchanged.</p>
    </div>

    <script>
        function checkCurrentAge() {
            try {
                const creationDate = localStorage.getItem('mapsApiKeyCreationDate');
                const lastHealthCheck = localStorage.getItem('mapsApiHealthCheck');
                
                if (creationDate) {
                    const age = Date.now() - parseInt(creationDate);
                    const ageDays = Math.floor(age / (24 * 60 * 60 * 1000));
                    
                    document.getElementById('current-status').innerHTML = `
                        <strong>Current Status:</strong><br>
                        Key Age: ${ageDays} days<br>
                        Created: ${new Date(parseInt(creationDate)).toLocaleDateString()}<br>
                        Last Health Check: ${lastHealthCheck ? new Date(parseInt(lastHealthCheck)).toLocaleDateString() : 'Never'}
                    `;
                } else {
                    document.getElementById('current-status').innerHTML = `
                        <strong>Current Status:</strong><br>
                        No creation date stored (key will be considered old)
                    `;
                }
            } catch (error) {
                document.getElementById('current-status').innerHTML = `
                    <strong>Error:</strong> ${error.message}
                `;
            }
        }

        function resetKeyAge() {
            try {
                const now = Date.now();
                localStorage.setItem('mapsApiKeyCreationDate', now.toString());
                localStorage.setItem('mapsApiHealthCheck', now.toString());
                
                document.getElementById('result').innerHTML = `
                    <div class="status success">
                        ✅ <strong>Success!</strong> API key age reset to today.<br>
                        New creation date: ${new Date(now).toLocaleDateString()}<br>
                        The rotation warning should stop appearing.
                    </div>
                `;
                
                // Update current status
                setTimeout(checkCurrentAge, 100);
                
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div class="status error">
                        ❌ <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        function clearAllData() {
            try {
                localStorage.removeItem('mapsApiKeyCreationDate');
                localStorage.removeItem('mapsApiHealthCheck');
                localStorage.removeItem('apiRotationEvents');
                localStorage.removeItem('mapsSecurityEvents');
                localStorage.removeItem('xssProtectionEvents');
                localStorage.removeItem('cspViolations');
                
                document.getElementById('result').innerHTML = `
                    <div class="status success">
                        ✅ <strong>Success!</strong> All rotation and security data cleared.<br>
                        The system will start fresh on next app load.
                    </div>
                `;
                
                // Update current status
                setTimeout(checkCurrentAge, 100);
                
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div class="status error">
                        ❌ <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Check current age on page load
        window.addEventListener('load', checkCurrentAge);
    </script>
</body>
</html>
