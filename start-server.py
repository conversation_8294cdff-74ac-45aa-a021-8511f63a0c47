#!/usr/bin/env python3
"""
Simple HTTP server for the Monoloci app
Run this script to serve the app on localhost:8000
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

PORT = 8000

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers to allow external resources
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def log_message(self, format, *args):
        # Custom logging
        print(f"[{self.address_string()}] {format % args}")

def main():
    # Change to the 'dist' directory to serve the built app
    dist_dir = Path(__file__).parent / 'dist'
    if not dist_dir.exists():
        print("❌ Error: 'dist' directory not found. Please run 'npm run build' first.")
        sys.exit(1)
    os.chdir(dist_dir)
    
    print("🚀 Starting Monoloci App Server")
    print(f"📁 Serving from: {dist_dir}")
    print(f"🌐 Server will run on: http://localhost:{PORT}")
    print()
    
    # Check if files exist
    files_to_check = ['index.html', 'app.js', 'dist/tailwind.css', 'test-basic.html', 'index-fallback.html', 'firebase-test.html']
    missing_files = []
    
    for file in files_to_check:
        if os.path.exists(file):
            print(f"✅ Found: {file}")
        else:
            print(f"❌ Missing: {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  Warning: {len(missing_files)} files are missing")
    
    print("\n📋 Available pages:")
    print(f"   • Main app: http://localhost:{PORT}/index.html")
    print(f"   • Diagnostics: http://localhost:{PORT}/test-basic.html")
    print(f"   • Firebase test: http://localhost:{PORT}/firebase-test.html")
    print(f"   • Fallback app: http://localhost:{PORT}/index-fallback.html")
    print()
    
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"🎯 Server started successfully on port {PORT}")
            print("🔗 Opening browser...")
            
            # Open browser to the diagnostic page first
            webbrowser.open(f'http://localhost:{PORT}/test-basic.html')
            
            print("\n💡 Tips:")
            print("   • Press Ctrl+C to stop the server")
            print("   • If the main app doesn't load, try the fallback version")
            print("   • Check the diagnostic page for specific issues")
            print("   • Make sure your firewall allows localhost connections")
            print()
            print("🔄 Server is running... (Press Ctrl+C to stop)")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"\n❌ Error: Port {PORT} is already in use")
            print("💡 Try:")
            print(f"   • Use a different port: python start-server.py --port 8001")
            print(f"   • Stop other servers running on port {PORT}")
        else:
            print(f"\n❌ Error starting server: {e}")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    # Simple argument parsing for port
    if len(sys.argv) > 1 and sys.argv[1] == '--port' and len(sys.argv) > 2:
        try:
            PORT = int(sys.argv[2])
        except ValueError:
            print("❌ Invalid port number")
            sys.exit(1)
    
    main()
