import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
import { getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
import { getFirestore, doc, setDoc, updateDoc, collection, addDoc, onSnapshot, query, serverTimestamp, getDoc, where, getDocs, arrayUnion } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";
import { getStorage, ref, uploadBytes, getDownloadURL } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-storage.js";
import { getAnalytics, logEvent } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-analytics.js";

// --- 1. CONFIGURATION & STATE ---
const appId = 'aura-global-app';
const firebaseConfig = window.FIREBASE_CONFIG || {
  // Fallback configuration - should be replaced by build process
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.FIREBASE_APP_ID
};
const initialAuthToken = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : null;

const GUIDE_USER_ID = 'the_official_guide_for_this_tour'; 

let db, auth, storage, userId, userLocation, analytics;
let isNativePlatform = false;
let watchPositionId, journeyId, unsubscribeFromMessages;
let isChatActive = false;
let currentActivityId = null;
let currentChatMode = 'group', currentViewMode = 'chat';
let emergencyContact = '', emergencyMessage = '', defaultSosAction = 'show-options';
let itinerary = [];
let authReadyPromise = null;
let mediaRecorder, audioChunks = [], isRecording = false;
// Promise-based approach to ensure Maps API is loaded before use.
const mapsApiReadyPromise = window.mapsApiReadyPromise || new Promise(resolve => {
    // If the promise isn't on the window, we set up a fallback.
    // The initMap function on window will be called regardless.
    window.initMap = () => {
        console.log("Google Maps API script loaded via app.js fallback.");
        resolve();
    };
});
let initTripPlannerMap;
let tripPlannerInitialCoords = null;
// --- 2. UI ELEMENT REFERENCES ---
const appContainer = document.getElementById('app-container');
const homeScreen = document.getElementById('home-screen');
const soloScreen = document.getElementById('solo-screen');
const groupScreen = document.getElementById('group-screen');
const infoScreen = document.getElementById('info-screen');
const userGuideScreen = document.getElementById('user-guide-screen');
const sponsorsScreen = document.getElementById('sponsors-screen');
const notesScreen = document.getElementById('notes-screen');
const itineraryList = document.getElementById('itinerary-list');
const activityInfoScreen = document.getElementById('activity-info-screen');
const chatContainer = document.getElementById('chat-container');
const codexScreen = document.getElementById('codex-screen');
const settingsModal = document.getElementById('settings-modal');
const sosOptionsModal = document.getElementById('sos-options-modal');
const actionConfirmModal = document.getElementById('action-confirm-modal');
const journeyModal = document.getElementById('journey-modal');
const photoGallery = document.getElementById('photo-gallery');
const galleryGrid = document.getElementById('gallery-grid');
const messageInputArea = document.getElementById('message-input-area');
const nameModal = document.getElementById('name-modal');
const nameInput = document.getElementById('name-input');
const saveNameButton = document.getElementById('save-name-button');
const pwaInstallModal = document.getElementById('pwa-install-modal');
const pwaInstallConfirmButton = document.getElementById('pwa-install-confirm-button');
const pwaInstallCancelButton = document.getElementById('pwa-install-cancel-button');
const pwaUpdateModal = document.getElementById('pwa-update-modal');
const pwaUpdateConfirmButton = document.getElementById('pwa-update-confirm-button');
const pwaUpdateCancelButton = document.getElementById('pwa-update-cancel-button');

const navHome = document.getElementById('nav-home');
const navSolo = document.getElementById('nav-solo');
const navGroup = document.getElementById('nav-group');
const navInfo = document.getElementById('nav-info');
const navCodex = document.getElementById('nav-codex');

const settingsButton = document.getElementById('settings-button');
const closeSettingsButton = document.getElementById('close-settings-button');
const settingsForm = document.getElementById('settings-form');
const emergencyContactInput = document.getElementById('emergency-contact');
const emergencyMessageInput = document.getElementById('emergency-message');

const homeSosButton = document.getElementById('home-sos-button');
const locationStatus = document.getElementById('location-status');
const cancelSos = document.getElementById('cancel-sos');
const alertContactButton = document.getElementById('alert-contact-button');
const actionConfirmTitle = document.getElementById('action-confirm-title');
const actionConfirmText = document.getElementById('action-confirm-text');
const closeActionConfirmModal = document.getElementById('close-action-confirm-modal');

const journeyStatusText = document.getElementById('journey-status-text');
const journeyLink = document.getElementById('journey-link');
const copyJourneyLinkButton = document.getElementById('copy-journey-link');
const stopJourneyButton = document.getElementById('stop-journey-button');
const soundScapeButton = document.getElementById('sound-scape-button');
const soundScapeStatus = document.getElementById('sound-scape-status');
const shareLocationButtonHome = document.getElementById('share-location-button-home');

const accessForm = document.getElementById('access-form');
const accessCodeInput = document.getElementById('access-code-input');
const errorMessage = document.getElementById('error-message');
const backToInfoBtn = document.getElementById('back-to-info');
const messageForm = document.getElementById('message-form');
const messageInput = document.getElementById('message-input');
const chatMessages = document.getElementById('chat-messages');
const loadingSpinner = document.getElementById('loading-spinner');
const chatModeSwitcher = document.getElementById('chat-mode-switcher');
const chatHeaderTitle = document.getElementById('chat-header-title');
const chatHeaderSubtitle = document.getElementById('chat-header-subtitle');
const modeGroupBtn = document.getElementById('mode-group');
const modePhotosBtn = document.getElementById('mode-photos');
const fileInput = document.getElementById('file-input');
const uploadStatus = document.getElementById('upload-status');
const regionSelect = document.getElementById('region-select');
const directoryList = document.getElementById('directory-list');
const directoryItemModal = document.getElementById('directory-item-modal');
const directoryItemContent = document.getElementById('directory-item-content');
const closeDirectoryModal = document.getElementById('close-directory-modal');

// --- 3. CORE FUNCTIONS ---

function showScreen(screenName) {
    homeScreen.classList.toggle('hidden', screenName !== 'home');
    soloScreen.classList.toggle('hidden', screenName !== 'solo');
    groupScreen.classList.toggle('hidden', screenName !== 'group');
    infoScreen.classList.toggle('hidden', screenName !== 'info');
    userGuideScreen.classList.toggle('hidden', screenName !== 'user-guide');
    sponsorsScreen.classList.toggle('hidden', screenName !== 'sponsors');
    notesScreen.classList.toggle('hidden', screenName !== 'notes');
    activityInfoScreen.classList.toggle('hidden', screenName !== 'activity-info');
    chatContainer.classList.toggle('hidden', screenName !== 'chat');
    codexScreen.classList.toggle('hidden', screenName !== 'codex');
    isChatActive = (screenName === 'chat');
    
    // Always re-render the itinerary when showing the group screen
    // to ensure the list is up-to-date after adding new activities.
    if (screenName === 'group') {
        renderItinerary();
    }

    navHome.classList.toggle('text-sky-600', screenName === 'home');
    navHome.classList.toggle('bg-sky-100', screenName === 'home');
    navHome.classList.toggle('text-slate-500', screenName !== 'home');
    navSolo.classList.toggle('text-sky-600', screenName === 'solo');
    navSolo.classList.toggle('bg-sky-100', screenName === 'solo');
    navSolo.classList.toggle('text-slate-500', screenName !== 'solo');
    navGroup.classList.toggle('text-sky-600', screenName === 'group' || screenName === 'activity-info' || screenName === 'chat');
    navGroup.classList.toggle('bg-sky-100', screenName === 'group' || screenName === 'activity-info' || screenName === 'chat');
    navGroup.classList.toggle('text-slate-500', screenName !== 'group' && screenName !== 'activity-info' && screenName !== 'chat');
    navInfo.classList.toggle('text-sky-600', ['info', 'user-guide', 'sponsors', 'notes'].includes(screenName));
    navInfo.classList.toggle('bg-sky-100', ['info', 'user-guide', 'sponsors', 'notes'].includes(screenName));
    navInfo.classList.toggle('text-slate-500', !['info', 'user-guide', 'sponsors', 'notes'].includes(screenName));

    navCodex.classList.toggle('text-sky-600', screenName === 'codex');
    navCodex.classList.toggle('bg-sky-100', screenName === 'codex');
    navCodex.classList.toggle('text-slate-500', screenName !== 'codex');
    navInfo.classList.toggle('text-sky-600', screenName === 'info' || screenName === 'user-guide' || screenName === 'sponsors' || screenName === 'notes');
    navInfo.classList.toggle('bg-sky-100', screenName === 'info' || screenName === 'user-guide' || screenName === 'sponsors' || screenName === 'notes');
    navInfo.classList.toggle('text-slate-500', !(screenName === 'info' || screenName === 'user-guide' || screenName === 'sponsors' || screenName === 'notes'));
}

function getItinerary() { return JSON.parse(localStorage.getItem('auraItinerary') || '[]'); }
function saveItinerary(itinerary) { localStorage.setItem('auraItinerary', JSON.stringify(itinerary)); }

function deleteActivity(activityId) {
    if (confirm('Are you sure you want to delete this activity?')) {
        itinerary = itinerary.filter(activity => activity.id !== activityId);
        saveItinerary(itinerary);
        renderItinerary();
    }
}

function renderItinerary() {
    itineraryList.innerHTML = '';
    if (itinerary.length === 0) {
        itineraryList.innerHTML = `<p class="text-center text-slate-500">Your itinerary is empty.</p>`;
        return;
    }
    itinerary.forEach(activity => {
        const activityCard = document.createElement('div');
        activityCard.className = 'flex items-center p-4 bg-white border rounded-lg shadow-sm';
        activityCard.innerHTML = `
            <div class="flex-1 cursor-pointer" data-activity-id="${activity.id}">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-sky-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                        ${activity.name.charAt(0).toUpperCase()}
                    </div>
                    <div class="flex-1">
                        <p class="font-bold text-slate-800">${activity.name}</p>
                        <p class="text-sm text-slate-600">${activity.date}</p>
                    </div>
                    <div class="text-slate-400">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m9 18 6-6-6-6"/>
                        </svg>
                    </div>
                </div>
            </div>
            <button class="delete-activity-button p-2 text-slate-400 hover:text-red-500" data-activity-id="${activity.id}">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="3 6 5 6 21 6"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/></svg>
            </button>
        `;
        
        const clickableArea = activityCard.querySelector('.flex-1.cursor-pointer');
        clickableArea.onclick = () => showActivityDetails(activity.id);

        const deleteButton = activityCard.querySelector('.delete-activity-button');
        deleteButton.onclick = () => deleteActivity(activity.id);

        itineraryList.appendChild(activityCard);
    });
}

function showActivityDetails(activityId) {
    console.log(`Attempting to show details for activityId: ${activityId}`);
    if (!activityId) {
        console.error('No activity ID provided');
        return;
    }
    
    const activity = itinerary.find(a => a.id === activityId);
    if (!activity) {
        console.error('Activity not found');
        return;
    }
    
    currentActivityId = activityId;
    // Show activity details screen
    showScreen('activity-info');
}

    // Build company section
    let companySection = `
        <p class="text-md text-slate-500 mt-1">from ${activity.company}</p>
    `;
    if (activity.company_logo) {
        companySection = `
            <div class="flex items-center gap-4 mt-2">
                <img src="${activity.company_logo}" alt="${activity.company} Logo" class="w-12 h-12 rounded-full" onerror="this.style.display='none';">
                <div>
                    <p class="text-sm text-slate-500">from</p>
                    <p class="font-bold text-slate-700">${activity.company}</p>
                </div>
            </div>
        `;
    }

     // Build guide section only if guide data exists
     let guideSection = '';
     if (activity.guide && (activity.guide.name || activity.guide.title)) {
         guideSection = `
             <div class="mt-6">
                 <h3 class="text-lg font-semibold text-slate-700">Your Guide</h3>
                 <div class="flex items-center gap-4 mt-2 p-4 bg-slate-50 rounded-lg">
                     <img src="${activity.guide_photo || 'https://placehold.co/64x64/a3e635/ffffff?text=G'}" alt="Guide" class="w-16 h-16 rounded-full" onerror="this.onerror=null;this.src='https://placehold.co/64x64/a3e635/ffffff?text=G';">
                     <div>
                         <p class="font-bold text-slate-800">${activity.guide?.name || 'Guide'}</p>
                         <p class="text-sm text-slate-600">${activity.guide?.title || ''}</p>
                     </div>
                 </div>
             </div>
         `;
     }

     // Use WordPress featured image if available, fallback to featured_image field
     const activityImage = activity.wp_featured_image || activity.featured_image || '';

     activityInfoScreen.innerHTML = `
        <div class="p-3 border-b border-slate-200 bg-slate-50 text-center flex items-center">
            <button id="info-back-to-itinerary" class="p-2 rounded-full hover:bg-slate-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-slate-600"><path d="m15 18-6-6 6-6"/></svg></button>
            <div class="flex-1"><h1 class="text-xl font-bold text-slate-800">Activity Details</h1></div><div class="w-10"></div>
        </div>
        <div class="flex-1 p-6 overflow-y-auto">
            ${activityImage ? `<img src="${activityImage}" class="w-full h-40 object-cover rounded-lg mb-4" onerror="this.style.display='none'">` : ''}
            <h2 class="text-2xl font-bold text-slate-800">${activity.name}</h2>
            ${companySection}
            ${guideSection}
            <div class="mt-6"><h3 class="text-lg font-semibold text-slate-700">About this Activity</h3><p class="text-slate-600 mt-2 text-sm leading-relaxed">${activity.description}</p></div>
            <div class="mt-6"><h3 class="text-lg font-semibold text-slate-700">Documents</h3><div id="document-list" class="mt-2 space-y-2"></div></div>
        </div>
        <div class="p-4 border-t bg-white"><button id="go-to-chat-button" class="w-full py-3 bg-sky-500 text-white rounded-lg font-semibold hover:bg-sky-600 active:scale-95">Go to Activity Chat</button></div>
     `;
     
     const docList = activityInfoScreen.querySelector('#document-list');
     if(activity.documents && activity.documents.length > 0){
        activity.documents.forEach(doc => {
            const docEl = document.createElement('div');
            docEl.className = 'flex items-center gap-3 p-3 bg-slate-100 rounded-lg hover:bg-slate-200 cursor-pointer';
            docEl.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="16" y1="13" x2="8" y2="13"/></svg><span>${escapeHTML(doc.name)}</span>`;
            
            // Only make it clickable if there's a valid URL
            if (doc.url && doc.url !== '#') {
                docEl.addEventListener('click', (e) => {
                    e.preventDefault();
                    window.open(doc.url, '_blank');
                });
            } else {
                docEl.classList.remove('hover:bg-slate-200', 'cursor-pointer');
                docEl.classList.add('opacity-50', 'cursor-not-allowed');
            }
            
            docList.appendChild(docEl);
        });
     } else {
        docList.innerHTML = `<p class="text-sm text-slate-500">No documents provided for this activity.</p>`
     }

     document.getElementById(`info-back-to-itinerary`).addEventListener('click', () => showScreen('group'));
     document.getElementById(`go-to-chat-button`).addEventListener('click', () => {
        chatHeaderTitle.textContent = 'Activity Chat';
        chatHeaderSubtitle.textContent = activity.name;
        showScreen('chat');
        if (auth) setupChat();
     });
document.getElementById('chat-back-to-activity-info').addEventListener('click', () => showScreen('activity-info'));
     showScreen('activity-info');

function loadSettings() {
    // Load and decode settings
    try {
        emergencyContact = localStorage.getItem('auraEmergencyContact') ? 
            atob(localStorage.getItem('auraEmergencyContact')) : '';
        emergencyMessage = localStorage.getItem('auraEmergencyMessage') ? 
            atob(localStorage.getItem('auraEmergencyMessage')) : 'I am in an emergency and need help. This is my location.';
    } catch (e) {
        // Fallback for existing unencoded data
        emergencyContact = localStorage.getItem('auraEmergencyContact') || '';
        emergencyMessage = localStorage.getItem('auraEmergencyMessage') || 'I am in an emergency and need help. This is my location.';
    }
    
    defaultSosAction = localStorage.getItem('auraDefaultSosAction') || 'show-options';

    // Sanitize loaded data
    emergencyContact = sanitizeInput(emergencyContact);
    emergencyMessage = sanitizeInput(emergencyMessage);

    if (emergencyContactInput) emergencyContactInput.value = emergencyContact;
    if (emergencyMessageInput) emergencyMessageInput.value = emergencyMessage;
}

function handleSaveSettings(e) {
    e.preventDefault();
    
    // Sanitize and validate inputs
    const newEmergencyContact = sanitizeInput(emergencyContactInput.value);
    const newEmergencyMessage = sanitizeInput(emergencyMessageInput.value);
    const newDefaultSosAction = document.querySelector('input[name="sos-action"]:checked')?.value || 'show-options';
    
    // Validate emergency contact
    if (newEmergencyContact && !validateEmergencyContact(newEmergencyContact)) {
        alert('Please enter a valid phone number for emergency contact.');
        return;
    }
    
    // Validate SOS action
    const validSosActions = ['show-options', 'send-sms', 'call-emergency'];
    if (!validSosActions.includes(newDefaultSosAction)) {
        console.error('Invalid SOS action');
        return;
    }
    
    // Update global variables
    emergencyContact = newEmergencyContact;
    emergencyMessage = newEmergencyMessage || 'I am in an emergency and need help. This is my location.';
    defaultSosAction = newDefaultSosAction;

    // Save to localStorage with encryption (basic obfuscation)
    localStorage.setItem('auraEmergencyContact', btoa(emergencyContact));
    localStorage.setItem('auraEmergencyMessage', btoa(emergencyMessage));
    localStorage.setItem('auraDefaultSosAction', defaultSosAction);
    
    console.log('Settings saved securely');
    alert('Settings saved successfully!');
    settingsModal.classList.add('hidden');
}

function handleSos() {
    const action = isChatActive ? 'show-options' : defaultSosAction;
    if (action === 'alert-contact') {
        triggerDefaultSosAction();
    } else {
        showSosOptions();
    }
}

function showSosOptions() {
    sosOptionsModal.classList.remove('hidden');
    locationStatus.textContent = 'Getting your location...';
    navigator.geolocation.getCurrentPosition(position => {
        userLocation = { latitude: position.coords.latitude, longitude: position.coords.longitude };
        locationStatus.textContent = 'Location acquired!';
    }, () => {
        userLocation = null;
        locationStatus.textContent = 'Could not get location. Please ensure you have a network connection or GPS signal.';
    });
}

async function triggerDefaultSosAction() {
    if (!emergencyContact) {
        actionConfirmTitle.textContent = "Action Failed";
        actionConfirmText.innerHTML = `<strong>Please set an emergency contact in Settings first.</strong>`;
        actionConfirmModal.classList.remove('hidden');
        return;
    }

    actionConfirmTitle.textContent = "SOS Action Triggered";
    actionConfirmText.textContent = 'Getting location and preparing alert...';
    actionConfirmModal.classList.remove('hidden');
    
    await handleSmsAlert(true); // Call the new async function
}

async function handleSmsAlert(isDefaultAction = false) {
    if (!emergencyContact) {
        alert('Please set an emergency contact in Settings first.');
        return;
    }

    const statusElement = isDefaultAction ? actionConfirmText : locationStatus;
    statusElement.textContent = 'Getting your location...';

    try {
        const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, { enableHighAccuracy: true });
        });
        const userLocation = { latitude: position.coords.latitude, longitude: position.coords.longitude };
        
        let fullMessage = `${emergencyMessage}`;
        fullMessage += `\nMy location: https://www.google.com/maps?q=${userLocation.latitude},${userLocation.longitude} `; // Added space

        const smsUrl = `sms:${emergencyContact}?body=${encodeURIComponent(fullMessage)}`;
        window.location.href = smsUrl;

        if (isDefaultAction) {
            actionConfirmText.innerHTML = `<strong>Opening messaging app to alert ${emergencyContact}...</strong>`;
        } else {
            sosOptionsModal.classList.add('hidden');
        }

    } catch (error) {
        console.error("Failed to get location for SMS:", error);
        statusElement.textContent = 'Could not get location. Sending alert without it.';
        // Still try to send message without location
        let fullMessage = `${emergencyMessage} (Location not available)`; // Added space
        const smsUrl = `sms:${emergencyContact}?body=${encodeURIComponent(fullMessage)}`;
        window.location.href = smsUrl;
    }
}

async function stopJourney() {
    if (watchPositionId) navigator.geolocation.clearWatch(watchPositionId);
    watchPositionId = null;
    if (journeyId && db) {
        const journeyDocRef = doc(db, `/artifacts/${appId}/public/data/journeys/${journeyId}`);
        await updateDoc(journeyDocRef, { active: false });
    }
    journeyModal.classList.add('hidden');
    journeyId = null;
}

function copyJourneyLink() {
    if (copyJourneyLinkButton.disabled) return;
    const textArea = document.createElement("textarea");
    textArea.value = journeyLink.textContent;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    alert('Link copied!');
}

async function handleAccessCode(e) {
    e.preventDefault();
    const enteredCode = accessCodeInput.value.trim().toUpperCase();
    if (!enteredCode) return;
    
    errorMessage.textContent = 'Searching...';

    try {
        await authReadyPromise;
        const activityPath = `artifacts/${appId}/public/data/activities`;
        const activityRef = doc(db, activityPath, enteredCode);
        const activitySnap = await getDoc(activityRef);

        if (activitySnap.exists()) {
            const activityData = activitySnap.data();
            // IMPORTANT: Ensure the activity has an 'id' field. Fallback to the code if it doesn't.
            if (!activityData.id) {
                activityData.id = enteredCode;
            }
            activityData.code = enteredCode;
            
            console.log('Activity found from Firebase:', activityData);
            
            const activityExists = itinerary.some(a => a.id === activityData.id);
            if (!activityExists) {
                itinerary.push(activityData);
                saveItinerary(itinerary);
                renderItinerary();
                // Save user name for chat
                nameModal.classList.remove('hidden');
                saveNameButton.onclick = () => {
                    const userName = nameInput.value.trim();
                    if (userName) {
                        localStorage.setItem('auraUserName', userName);
                        nameModal.classList.add('hidden');
                        showActivityDetails(activityData.id);
                    }
                };
            }
            
            accessCodeInput.value = '';
            errorMessage.textContent = '';
            
            // This must be called after the state is updated.
            showActivityDetails(activityData.id);

        } else {
            console.log('Activity not found for code:', enteredCode);
            errorMessage.textContent = 'Invalid Activity Code.';
        }
    } catch (error) {
        console.error("Firebase fetch error or subsequent processing error:", error);
        errorMessage.textContent = 'Error finding activity.';
    }
}

function initializeFirebase() {
    authReadyPromise = new Promise(async (resolve) => {
        try {
            // Check for Capacitor and set the platform flag
            isNativePlatform = window.Capacitor && window.Capacitor.isNativePlatform();

            const app = initializeApp(firebaseConfig);
            auth = getAuth(app);
            db = getFirestore(app);
            storage = getStorage(app);

            // EXPOSE GLOBALLY FOR THE CODEX FEATURE
            window.auraFirebase = {
                db,
                auth,
                // Firestore functions
                collection,
                getDocs,
                doc,
                getDoc,
                updateDoc,
                arrayUnion,
                setDoc,
                serverTimestamp,
                // Auth functions
                onAuthStateChanged
            };

            if (isNativePlatform) {
                console.log("Capacitor native platform detected. Analytics will use the native plugin.");
                // For native, Firebase is initialized via google-services.json / GoogleService-Info.plist
                // and the Capacitor Firebase plugins handle communication. Web analytics is not needed.
            } else {
                console.log("Web platform detected. Initializing Firebase Web Analytics.");
                analytics = getAnalytics(app);
            }

            trackDisplayMode(); // Track launch event for both platforms

            onAuthStateChanged(auth, async (user) => {
                if (user) { 
                    userId = user.uid; 
                    resolve(); 
                } else {
                    if (initialAuthToken) {
                        try {
                            await signInWithCustomToken(auth, initialAuthToken);
                        } catch (error) { console.error("Auth Error:", error); }
                    } else {
                        try {
                            await signInAnonymously(auth);
                        } catch (error) { console.error("Auth Error:", error); }
                    }
                }
            });
        } catch (error) { console.error("Firebase Init Error:", error); }
    });

    // Dispatch a custom event to signal that Firebase is fully initialized
    // This is crucial for other scripts that depend on it, like the Codex feature.
    window.dispatchEvent(new CustomEvent('firebase-ready'));
    console.log("Dispatched firebase-ready event.");
}

function generatePrivateChatId(uid1, uid2) { return [uid1, uid2].sort().join('_'); }

async function setupChat() {
    if (!db || !userId) return;
    
    // For group chats, verify user has access to this activity
    if (currentChatMode === 'group') {
        const userHasActivity = itinerary.some(a => a.id === currentActivityId);
        if (!userHasActivity) {
            console.error('User does not have access to this activity chat');
            return;
        }
    }
    
    // Initialize UI state
    updateUIForChatView();
    
    chatMessages.innerHTML = '';
    loadingSpinner.style.display = 'flex';

    // Use original Firebase path structure
    let messagesCollectionPath = `artifacts/${appId}/public/data/aura-chat-rooms/${currentActivityId}/messages`;

    console.log('Setting up chat with path:', messagesCollectionPath);
    console.log('User ID:', userId);
    console.log('Activity ID:', currentActivityId);

    const messagesCollection = collection(db, messagesCollectionPath);
    const q = query(messagesCollection);
    
    unsubscribeFromMessages = onSnapshot(q, (querySnapshot) => {
        loadingSpinner.style.display = 'none';
        const messages = [];
        querySnapshot.forEach((doc) => {
            const data = doc.data();
            console.log('Message data:', data);
            messages.push({ id: doc.id, ...data });
        });
        messages.sort((a, b) => (a.timestamp?.seconds || 0) - (b.timestamp?.seconds || 0));
        console.log('Received messages:', messages.length, messages);
        renderMessages(messages);
    }, (error) => {
        console.error("Error fetching messages:", error);
        loadingSpinner.style.display = 'none';
        chatMessages.innerHTML = `<p class="text-red-500 text-center">Error loading messages: ${error.message}<br><small>Path: ${messagesCollectionPath}</small></p>`;
    });
}

async function compressImage(file, options = {}) {
    return new Promise((resolve, reject) => {
        const { quality = 0.7, maxWidth = 1920, maxHeight = 1080 } = options;
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (event) => {
            const img = new Image();
            img.src = event.target.result;
            img.onload = () => {
                const canvas = document.createElement('canvas');
                let { width, height } = img;

                if (width > height) {
                    if (width > maxWidth) {
                        height *= maxWidth / width;
                        width = maxWidth;
                    }
                } else {
                    if (height > maxHeight) {
                        width *= maxHeight / height;
                        height = maxHeight;
                    }
                }

                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);

                canvas.toBlob((blob) => {
                    if (blob) {
                        resolve(blob);
                    } else {
                        reject(new Error('Canvas to Blob conversion failed'));
                    }
                }, 'image/jpeg', quality);
            };
            img.onerror = (error) => reject(error);
        };
        reader.onerror = (error) => reject(error);
    });
}

async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file || !userId) {
      console.error('Cannot upload file: missing file or userId', { file: !!file, userId });
      uploadStatus.textContent = 'Upload failed: missing user authentication. Please try again.';
      setTimeout(() => uploadStatus.classList.add('hidden'), 3000);
      return;
    }

    uploadStatus.textContent = `Uploading ${file.name}...`;
    uploadStatus.classList.remove('hidden');

    try {
        let fileToUpload = file;
        const originalFileName = file.name;
        let finalStorageFileName = `${userId}-${Date.now()}-${originalFileName}`; // Default for non-image files

        if (file.type.startsWith('image/')) {
            const compressedBlob = await compressImage(file);
            fileToUpload = compressedBlob;
            const baseName = originalFileName.substring(0, originalFileName.lastIndexOf('.')) || originalFileName;
            // A robust, simple filename for the compressed image
            finalStorageFileName = `${userId}-${Date.now()}-compressed.jpg`;
        }

        const storageRef = ref(storage, `chat_uploads/${currentActivityId}/${finalStorageFileName}`);
        const snapshot = await uploadBytes(storageRef, fileToUpload);
        const downloadURL = await getDownloadURL(snapshot.ref);

        let messageData = {
            senderId: userId,
            senderName: localStorage.getItem('auraUserName') || 'Anonymous',
            timestamp: serverTimestamp(),
            fileName: originalFileName, // Keep original filename for display
            fileURL: downloadURL,
            type: file.type.startsWith('image/') ? 'image' : 'file'
        };

        await sendStructuredMessage(messageData);
        uploadStatus.classList.add('hidden');
    } catch(error) {
        console.error("Upload failed:", error);
        uploadStatus.textContent = 'Upload failed. Please try again.';
        setTimeout(() => uploadStatus.classList.add('hidden'), 3000);
    }
}

async function sendStructuredMessage(data) {
    if (!userId || !auth.currentUser) {
        console.error('User not authenticated for messaging');
        return;
    }
    
    const sanitizedData = {
        senderId: userId, // Must match authenticated user
        senderName: sanitizeInput(data.senderName || 'Anonymous'),
        timestamp: serverTimestamp(),
        // ... rest of data
    };
    
    // Handle different message types securely
    if (data.text) {
        sanitizedData.text = sanitizeInput(data.text);
        sanitizedData.type = 'text';
    } else if (data.fileURL) {
        // Validate file URL is from our storage
        if (data.fileURL.includes('firebasestorage.googleapis.com') && 
            data.fileURL.includes('aura-app-backend.firebasestorage.app')) {
            sanitizedData.fileURL = data.fileURL;
            sanitizedData.fileName = sanitizeInput(data.fileName || 'file');
            sanitizedData.type = data.type === 'image' ? 'image' : 'file';
        } else {
            console.error('Invalid file URL');
            return;
        }
    } else {
        console.error('Message must contain text or fileURL');
        return;
    }
    
    let messagesCollectionPath = `artifacts/${appId}/public/data/aura-chat-rooms/${currentActivityId}/messages`;
    
    try {
        const docRef = await addDoc(collection(db, messagesCollectionPath), sanitizedData);
        console.log('Message sent successfully with ID:', docRef.id);
    } catch (error) { 
        console.error("Error sending message: ", error);
    }
}

// Store messages locally to prevent disappearing
let localMessages = [];

function renderMessages(messages) {
    // Update local messages store
    localMessages = [...messages];
    
    chatMessages.innerHTML = '';
    galleryGrid.innerHTML = '';
    
    const images = messages.filter(m => m.type === 'image');
    if (images.length === 0) {
        galleryGrid.innerHTML = `<p class="col-span-3 text-center text-slate-500 text-sm p-4">No photos have been shared yet.</p>`;
    } else {
        images.forEach(msg => {
            const imgElement = document.createElement('div');
            imgElement.className = 'aspect-square bg-cover bg-center rounded-md cursor-pointer';
            imgElement.style.backgroundImage = `url(${msg.fileURL})`;
            imgElement.onclick = () => window.open(msg.fileURL, '_blank');
            galleryGrid.appendChild(imgElement);
        });
    }

    if (currentViewMode === 'chat') {
         if (messages.length === 0) {
            const welcomeText = currentChatMode === 'group' ? 'Welcome to the group chat!' : 'This is a private chat with your guide.';
            chatMessages.innerHTML = `<p class="text-center text-slate-500 text-sm p-4">${welcomeText}</p>`;
        } else {
            console.log('Rendering', messages.length, 'messages');
            messages.forEach((msg, index) => {
                let messageElement;
                const isCurrentUser = msg.senderId === userId;
                const alignmentClass = isCurrentUser ? 'items-end' : 'items-start';
                const bubbleColor = isCurrentUser ? 'bg-sky-500 text-white' : 'bg-slate-200 text-slate-800';
                const senderName = isCurrentUser ? 'You' : msg.senderName || 'User';

                if (msg.type !== 'image') { // Only render non-image messages in the chat view
                    if (msg.type === 'SOS') {
                        messageElement = document.createElement('div');
                        messageElement.className = 'my-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-800 rounded-r-lg w-full';
                        messageElement.innerHTML = `<p class="font-bold text-lg">🆘 SOS ALERT 🆘</p><p class="text-sm">Sent by ${escapeHTML(msg.senderRole)}.</p><p class="text-sm mt-1">Location: <a href="https://www.google.com/maps?q=${msg.location.latitude},${msg.location.longitude}" target="_blank" class="underline hover:text-red-600">View on Map</a></p>`;
                    } else {
                        messageElement = document.createElement('div');
                        messageElement.className = `flex flex-col mb-4 ${alignmentClass}`;
                        messageElement.setAttribute('data-message-id', msg.id || index);
                        let contentHtml;
                        if (msg.type === 'file') {
                            contentHtml = `<a href="${msg.fileURL}" target="_blank" class="flex items-center gap-2 p-3 rounded-lg ${isCurrentUser ? 'bg-sky-600' : 'bg-slate-300'}"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><line x1="10" y1="9" x2="8" y2="9"/></svg><span>${escapeHTML(msg.fileName)}</span></a>`;
                        } else {
                             contentHtml = `<p class="text-sm">${escapeHTML(msg.text || 'No message content')}</p>`;
                        }
                        messageElement.innerHTML = `<div class="text-xs text-slate-500 mb-1 px-1">${senderName}</div><div class="p-3 rounded-2xl message-bubble ${bubbleColor}">${contentHtml}</div>`;
                    }
                    chatMessages.appendChild(messageElement);
                }
            });
        }
        // Scroll to bottom after a short delay to ensure rendering is complete
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 100);
    }
}

async function handleMessageSubmit(e) {
    e.preventDefault();
    const messageText = messageInput.value.trim();
    if (!messageText) return;

    const tempId = `temp_${Date.now()}`;
    const optimisticMessage = {
        id: tempId,
        text: messageText,
        senderId: userId,
        timestamp: { seconds: Date.now() / 1000 },
        isOptimistic: true
    };

    localMessages.push(optimisticMessage);
    renderMessages(localMessages);
    messageInput.value = '';

    try {
        await sendStructuredMessage({
            text: messageText,
            senderId: userId,
            senderName: localStorage.getItem('auraUserName') || 'Anonymous',
            timestamp: serverTimestamp()
        });
        console.log('Message sent successfully');
    } catch (error) {
        console.error('Error sending message:', error);
        // Handle error: remove optimistic message or show error state
        localMessages = localMessages.filter(m => m.id !== tempId);
        renderMessages(localMessages);
    }
}

function escapeHTML(str) {
    if (!str) return '';
    const p = document.createElement('p');
    p.appendChild(document.createTextNode(str));
    return p.innerHTML;
}

function updateUIForChatView() {
    const isPhotos = currentViewMode === 'photos';
    
    modeGroupBtn.classList.toggle('bg-white', currentChatMode === 'group' && !isPhotos);
    modePhotosBtn.classList.toggle('bg-white', isPhotos);
    
    modeGroupBtn.classList.toggle('shadow', currentChatMode === 'group' && !isPhotos);
    modePhotosBtn.classList.toggle('shadow', isPhotos);

    chatMessages.classList.toggle('hidden', isPhotos);
    photoGallery.classList.toggle('hidden', !isPhotos);
    
    // Toggle visibility of input areas
    const photoUploadArea = document.getElementById('photo-upload-area');
    messageInputArea.classList.toggle('hidden', isPhotos);
    photoUploadArea.classList.toggle('hidden', !isPhotos);
    photoUploadArea.classList.toggle('flex', isPhotos);


}

async function handleSoundScape() {
    soundScapeStatus.textContent = '';

    if (isRecording) {
        mediaRecorder.stop();
        isRecording = false;
        soundScapeButton.innerHTML = 'Sound<br>Scape';
        soundScapeStatus.textContent = 'Recording stopped. Saving...';
        return;
    }

    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        isRecording = true;
        audioChunks = [];
        mediaRecorder = new MediaRecorder(stream);

        mediaRecorder.ondataavailable = event => {
            audioChunks.push(event.data);
        };

        mediaRecorder.onstop = () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            const audioUrl = URL.createObjectURL(audioBlob);
            const a = document.createElement('a');
            a.href = audioUrl;
            a.download = `SoundScape-${new Date().toISOString()}.wav`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(audioUrl);
            soundScapeStatus.textContent = 'Recording saved to downloads.';
            // Get all audio tracks and stop them to turn off the mic indicator
            stream.getTracks().forEach(track => track.stop());
            setTimeout(() => { soundScapeStatus.textContent = ''; }, 4000);
        };

        mediaRecorder.start();
        soundScapeButton.innerHTML = 'Stop<br>Recording';
        soundScapeStatus.textContent = 'Recording...';

    } catch (error) {
        console.error("Error accessing microphone:", error);
        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            soundScapeStatus.textContent = 'Microphone permission denied.';
        } else {
            soundScapeStatus.textContent = 'Could not start recording.';
        }
        isRecording = false;
    }
}

// --- 4. EVENT LISTENERS & INITIALIZATION ---

function showUpdateNotification(worker) {
    if (pwaUpdateModal && pwaUpdateConfirmButton && pwaUpdateCancelButton) {
        pwaUpdateModal.classList.remove('hidden');
        pwaUpdateConfirmButton.onclick = () => {
            pwaUpdateModal.classList.add('hidden');
            if (worker) {
                // Tell the new service worker to take over...
                worker.postMessage({ type: 'SKIP_WAITING' });
                // ...and then immediately reload the page to apply the update.
                window.location.reload();
            }
        };
        pwaUpdateCancelButton.onclick = () => {
            pwaUpdateModal.classList.add('hidden');
        };
    } else {
        // Fallback for when the modal isn't in the DOM
        if (confirm('New version available! Refresh to update?')) {
            worker.postMessage({ type: 'SKIP_WAITING' });
        }
    }
}

/**
 * Tracks how the app is launched and logs the event to the appropriate analytics service
 * (Capacitor plugin for native, Web SDK for browsers).
 */
function trackDisplayMode() {
    let displayMode = 'browser';
    const isPWA = window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone || document.referrer.includes('android-app://');

    if (isPWA) {
        displayMode = 'standalone';
    } else if (window.matchMedia('(display-mode: minimal-ui)').matches) {
        displayMode = 'minimal-ui';
    } else if (window.matchMedia('(display-mode: fullscreen)').matches) {
        displayMode = 'fullscreen';
    }

    const eventName = 'app_launched';
    const eventParams = { display_mode: displayMode };

    // Use Capacitor Firebase Analytics plugin if on a native platform
    if (isNativePlatform && window.FirebaseAnalytics) {
        console.log(`Capacitor Analytics: Logging event '${eventName}'`, eventParams);
        window.FirebaseAnalytics.logEvent({
            name: eventName,
            params: eventParams
        }).catch(err => console.error("Capacitor analytics error:", err));
    }
    // Fallback to Web SDK for PWA/browser
    else if (analytics) {
        console.log(`Web Analytics: Logging event '${eventName}'`, eventParams);
        try {
            logEvent(analytics, eventName, eventParams);
        } catch (error) {
            console.error("Firebase Web Analytics logging failed.", error);
        }
    } else {
        // This case might happen if the function is called before initialization is complete
        console.log(`Analytics: App launched in '${displayMode}' mode. (Analytics service not yet available).`);
    }
}

document.addEventListener('DOMContentLoaded', () => {
    itinerary = getItinerary();
    
    loadSettings();
    initializeFirebase();
    renderItinerary();
    
    // Register Service Worker for PWA functionality
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered: ', registration);
                    // Check for updates
                    registration.onupdatefound = () => {
                        const installingWorker = registration.installing;
                        if (installingWorker) {
                            installingWorker.onstatechange = () => {
                                if (installingWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New content is available, prompt user to refresh
                                    showUpdateNotification(installingWorker);
                                }
                            };
                        }
                    };
                }).catch(registrationError => {
                    console.log('SW registration failed: ', registrationError);
                });

            // This event fires when the service worker controlling the page changes.
            // We reload the page to ensure the new service worker's assets are used.
            // The 'controllerchange' event listener for reloading the page has been removed.
            // The reload is now handled directly by the update confirmation button's
            // onclick handler to provide a more reliable, single-refresh update process.
            let refreshing;
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                if (refreshing) return;
                // The reload is now handled by the button's onclick handler.
                // window.location.reload();
                refreshing = true;
            });
        });
    }
    
    // Handle install prompt for PWA
    let deferredPrompt;
    const installPwaButton = document.getElementById('install-pwa-button');

    // Check PWA installability
    function checkPWAInstallability() {
        // Check if already installed
        if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true) {
            console.log('PWA: App is already installed');
            return;
        }

        // Log basic PWA status
        console.log('PWA: Checking installability requirements...');

        // Show install button if no beforeinstallprompt after delay
        setTimeout(() => {
            if (!deferredPrompt) {
                console.log('PWA: Install prompt not available (normal for some browsers like Firefox)');
            }
        }, 3000);
    }

    window.addEventListener('beforeinstallprompt', (e) => {
        console.log('PWA: beforeinstallprompt event fired');
        // Prevent the mini-infobar from appearing on mobile
        e.preventDefault();
        // Stash the event so it can be triggered later.
        deferredPrompt = e;

        // Don't show the prompt if the app is already installed
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true) {
            console.log('PWA: App already installed, not showing prompt');
            return;
        }

        // Show the install prompt modal after a short delay to ensure DOM is ready
        setTimeout(() => {
            if (pwaInstallModal) {
                console.log('PWA: Showing install modal');
                pwaInstallModal.classList.remove('hidden');
            }
            // Update the UI to show the install button in the info screen as a fallback
            if (installPwaButton) {
                console.log('PWA: Showing install button');
                installPwaButton.classList.remove('hidden');
            }
        }, 1000); // 1 second delay to ensure user sees the app first
    });

    // Run installability check after page load
    window.addEventListener('load', () => {
        setTimeout(checkPWAInstallability, 2000);
    });

    if (installPwaButton) {
        installPwaButton.addEventListener('click', async () => {
            console.log('PWA: Install button clicked');
            if (!deferredPrompt) {
                console.log('PWA: No deferred prompt available - app may not be installable or already installed');
                return;
            }

            try {
                // Hide the app provided install button
                installPwaButton.classList.add('hidden');
                // Show the install prompt
                await deferredPrompt.prompt();
                // Wait for the user to respond to the prompt
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`PWA: User response to the install prompt: ${outcome}`);
                // We've used the prompt, and can't use it again, throw it away
                deferredPrompt = null;
            } catch (error) {
                console.error('PWA: Error during installation:', error);
                // Show the button again if there was an error
                installPwaButton.classList.remove('hidden');
            }
        });
    }

    if (pwaInstallConfirmButton) {
        pwaInstallConfirmButton.addEventListener('click', async () => {
            console.log('PWA: Install confirm button clicked');
            if (!deferredPrompt) {
                console.log('PWA: No deferred prompt available');
                pwaInstallModal.classList.add('hidden');
                return;
            }

            try {
                await deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`PWA: User response to the install prompt: ${outcome}`);
                deferredPrompt = null;
                pwaInstallModal.classList.add('hidden');
            } catch (error) {
                console.error('PWA: Error during installation:', error);
                pwaInstallModal.classList.add('hidden');
            }
        });
    }

    if (pwaInstallCancelButton) {
        pwaInstallCancelButton.addEventListener('click', () => {
            console.log('PWA: Install cancelled by user');
            pwaInstallModal.classList.add('hidden');
        });
    }

    // Handle successful PWA installation
    window.addEventListener('appinstalled', (evt) => {
        console.log('PWA: App was installed successfully');
        // Hide the app-provided install button
        if (installPwaButton) {
            installPwaButton.classList.add('hidden');
        }
        if (pwaInstallModal) {
            pwaInstallModal.classList.add('hidden');
        }
        deferredPrompt = null;
    });

    // --- SOLO TRIP PLANNER LOGIC ---
    const soloScreenContainer = document.getElementById('solo-screen');
    if (soloScreenContainer) {
        const addStopBtn = document.getElementById('addStopBtn');
        const calculateTravelBtn = document.getElementById('calculateTravelBtn');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const stopsTableBody = document.querySelector('#stopsTable tbody');
        const travelSummary = document.getElementById('travel-summary');
        let stops = [];
        let map, geocoder, directionsRenderer;
        let tripMarkers = []; // Array to store all markers for the trip planner

        // New global function to be called from other parts of the app, like Codex
        window.navigateToMap = (lat, lng) => {
            tripPlannerInitialCoords = { lat, lng };
            // Switch to the trip planner screen and initialize the map
            showScreen('solo');
            initTripPlannerMap();
        };

        initTripPlannerMap = async () => {
            try {
                // Check if Google Maps is already loaded or being loaded
                if (!window.google || !window.google.maps) {
                    // Check if script is already in DOM
                    const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
                    
                    if (!existingScript) {
                        await new Promise((resolve, reject) => {
                            const script = document.createElement('script');
                            const mapsApiKey = window.GOOGLE_MAPS_CONFIG?.apiKey || process.env.GOOGLE_MAPS_API_KEY;
                            if (!mapsApiKey) {
                                reject(new Error('Google Maps API key not configured'));
                                return;
                            }
                            script.src = `https://maps.googleapis.com/maps/api/js?key=${mapsApiKey}&libraries=marker,geometry&loading=async`;
                            script.async = true;
                            script.defer = true;
                            script.onload = resolve;
                            script.onerror = reject;
                            document.head.appendChild(script);
                        });
                    } else {
                        // Wait for existing script to load
                        await new Promise(resolve => {
                            if (window.google && window.google.maps) {
                                resolve();
                            } else {
                                existingScript.addEventListener('load', resolve);
                            }
                        });
                    }
                }

                // Wait for Google Maps to be fully loaded
                await new Promise(resolve => {
                    const checkGoogleMaps = () => {
                        if (window.google && window.google.maps && window.google.maps.Geocoder) {
                            resolve();
                        } else {
                            setTimeout(checkGoogleMaps, 100);
                        }
                    };
                    checkGoogleMaps();
                });

                const initialCenter = tripPlannerInitialCoords
                    ? { lat: tripPlannerInitialCoords.lat, lng: tripPlannerInitialCoords.lng }
                    : { lat: -34.9285, lng: 138.6007 };
                
                const initialZoom = tripPlannerInitialCoords ? 14 : 7;

                // If map already exists, just update its view
                if (map) {
                    map.setCenter(initialCenter);
                    map.setZoom(initialZoom);
                    if (tripPlannerInitialCoords) {
                        const marker = new google.maps.marker.AdvancedMarkerElement({
                            position: initialCenter,
                            map: map,
                            title: "Selected Location"
                        });
                        tripMarkers.push(marker);
                    }
                    tripPlannerInitialCoords = null;
                    return;
                }

                // Create new map
                geocoder = new google.maps.Geocoder();
                directionsRenderer = new google.maps.DirectionsRenderer();

                const mapOptions = {
                    center: initialCenter,
                    zoom: initialZoom,
                    disableDefaultUI: true,
                    zoomControl: true,
                    mapId: '9a6789cf3ff1289e'
                };
                map = new google.maps.Map(document.getElementById('map'), mapOptions);
                directionsRenderer.setMap(map);

                if (tripPlannerInitialCoords) {
                    const marker = new google.maps.marker.AdvancedMarkerElement({
                        position: initialCenter,
                        map: map,
                        title: "Selected Location"
                    });
                    tripMarkers.push(marker);
                }

                // Trigger resize immediately after creation
                google.maps.event.trigger(map, 'resize');
                map.setCenter(mapOptions.center);

                map.addListener('click', (e) => {
                    const latLng = e.latLng;
                    geocoder.geocode({ location: latLng }, (results, status) => {
                        if (status === 'OK') {
                            if (results[0]) {
                                document.getElementById('locationName').value = results[0].formatted_address;
                            } else {
                                alert('No results found for this location.');
                            }
                        } else {
                            alert('Geocoder failed due to: ' + status);
                        }
                    });
                });
            } catch (error) {
                console.error("Error initializing Google Map:", error);
                document.getElementById('map').innerHTML = '<p class="p-4 text-center text-red-600">Could not load map. Please check your internet connection and API key.</p>';
            } finally {
                tripPlannerInitialCoords = null; // Reset after use
            }
        };

        const addStopHandler = () => {
            if (stops.length >= 10) {
                alert("You can add a maximum of 10 stops.");
                return;
            }
            const locationName = document.getElementById('locationName').value.trim();
            const arrivalTime = document.getElementById('arrivalTime').value;
            const duration = document.getElementById('duration').value.trim();
            if (!locationName) {
                alert("Please enter an attraction name.");
                return;
            }
            stops.push({ locationName, arrivalTime, duration });
            renderStops();
            clearForm();
            analyzeBtn.disabled = true;
        };

        addStopBtn.addEventListener('click', addStopHandler);

        calculateTravelBtn.addEventListener('click', () => {
            const originalButtonText = "Calculate Travel";
            calculateTravelBtn.textContent = "Calculating...";
            calculateTravelBtn.disabled = true;

            if (stops.length < 2) {
                alert("Please add at least two stops to calculate travel times.");
                calculateTravelBtn.textContent = originalButtonText;
                calculateTravelBtn.disabled = false;
                return;
            }
            calculateAndDisplayRoute(stops, calculateTravelBtn, originalButtonText);
        });

        analyzeBtn.addEventListener('click', () => {
            analyzeItinerary(stops);
        });

        const getDirectionsBtn = document.getElementById('getDirectionsBtn');
        if (getDirectionsBtn) {
            getDirectionsBtn.addEventListener('click', () => {
                if (stops.length < 2) {
                    alert("Please add at least two stops to get directions.");
                    return;
                }
                const origin = encodeURIComponent(stops[0].locationName);
                const destination = encodeURIComponent(stops[stops.length - 1].locationName);
                const waypoints = stops.slice(1, -1).map(stop => encodeURIComponent(stop.locationName)).join('|');
                
                let googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}`;
                if (waypoints) {
                    googleMapsUrl += `&waypoints=${waypoints}`;
                }
                
                window.open(googleMapsUrl, '_blank');
            });
        }

        function calculateAndDisplayRoute(currentStops, btn, btnText) {
            if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                window.alert('Google Maps API failed to load. Please check your API key and project configuration.');
                btn.textContent = btnText;
                btn.disabled = false;
                return;
            }

            const directionsService = new google.maps.DirectionsService();
            const origin = currentStops[0].locationName;
            const destination = currentStops[currentStops.length - 1].locationName;
            const waypoints = currentStops.slice(1, -1).map(stop => ({
                location: stop.locationName,
                stopover: true
            }));

            directionsService.route({
                origin: origin,
                destination: destination,
                waypoints: waypoints,
                travelMode: google.maps.TravelMode.DRIVING
            }, (response, status) => {
                if (status === 'OK') {
                    let totalDurationSeconds = 0;
                    response.routes[0].legs.forEach((leg, index) => {
                        currentStops[index].travelToNext = leg.duration;
                        totalDurationSeconds += leg.duration.value;
                    });

                    renderStops();

                    const hours = Math.floor(totalDurationSeconds / 3600);
                    const minutes = Math.floor((totalDurationSeconds % 3600) / 60);
                    const totalTravelTime = `${hours} hours and ${minutes} minutes`;
                    // Instead of generating a link, render the route on the map
                    directionsRenderer.setDirections(response);

                    travelSummary.innerHTML = `
                        <h3 class="text-lg font-semibold">Travel Summary</h3>
                        <p class="text-slate-600 mt-2">Total estimated travel time: <strong>${totalTravelTime}</strong></p>
                    `;
                    
                    analyzeBtn.disabled = false;
                    if (getDirectionsBtn) getDirectionsBtn.disabled = false;
                } else {
                    window.alert('Directions request failed due to ' + status);
                }
                btn.textContent = btnText;
                btn.disabled = false;
            });
        }

        function shortenAddress(address) {
            if (!address) return '';
            // Remove Australia from the end, case-insensitive
            let shortened = address.replace(/, Australia$/i, '');
            // Remove state and postcode (e.g., ", SA 5007" or " SA 5007")
            shortened = shortened.replace(/,?\s[A-Z]{2,3}\s\d{4}$/, '');
            return shortened;
        }

        function analyzeItinerary(currentStops) {
            const resultsContainer = document.getElementById('analysis-results');
            resultsContainer.innerHTML = '';
            let analysisHTML = '<h3 class="text-lg font-semibold mb-2">Itinerary Analysis</h3><ul class="space-y-2">';
            let hasIssues = false;

            for (let i = 0; i < currentStops.length - 1; i++) {
                const stopA = currentStops[i];
                const stopB = currentStops[i + 1];

                if (stopA.travelToNext && stopA.arrivalTime && stopA.duration && stopB.arrivalTime) {
                    const travelTimeSeconds = stopA.travelToNext.value;
                    const arrivalA = new Date(`1970-01-01T${stopA.arrivalTime}`);
                    const durationHours = parseFloat(stopA.duration);
                    
                    if (isNaN(durationHours)) continue;

                    const departureA = new Date(arrivalA.getTime() + durationHours * 3600000);
                    const arrivalB = new Date(`1970-01-01T${stopB.arrivalTime}`);
                    const expectedArrivalB = new Date(departureA.getTime() + travelTimeSeconds * 1000);

                    if (expectedArrivalB > arrivalB) {
                        hasIssues = true;
                        const lateMinutes = Math.ceil((expectedArrivalB - arrivalB) / 60000);
                        const lateMessage = `You would arrive approximately <strong>${lateMinutes} minute${lateMinutes !== 1 ? 's' : ''}</strong> beyond your stated arrival time.`;
                        const locationA = shortenAddress(stopA.locationName);
                        const locationB = shortenAddress(stopB.locationName);
                        analysisHTML += `<li class="p-3 bg-red-100 rounded-md"><span class="font-bold text-red-700">Conflict:</span> You might not have enough time to get from <strong>${locationA}</strong> to <strong>${locationB}</strong>. Estimated travel time is ${stopA.travelToNext.text}. ${lateMessage}</li>`;
                    } else {
                        const spareTimeMinutes = (arrivalB - expectedArrivalB) / 60000;
                        if (spareTimeMinutes > 15) {
                            const locationA = shortenAddress(stopA.locationName);
                            const locationB = shortenAddress(stopB.locationName);
                            analysisHTML += `<li class="p-3 bg-green-100 rounded-md"><span class="font-bold text-green-700">Suggestion:</span> You have about <strong>${Math.round(spareTimeMinutes)} minutes</strong> of spare time before you need to leave for <strong>${locationB}</strong>. You could extend your stay at <strong>${locationA}</strong>.</li>`;
                        }
                    }
                }
            }

            if (!hasIssues) {
                analysisHTML += `<li class="p-3 bg-sky-100 rounded-md"><span class="font-bold text-sky-700">All Good:</span> Your itinerary looks feasible!</li>`;
            }
            analysisHTML += '</ul>';
            resultsContainer.innerHTML = analysisHTML;
        }

        function renderStops() {
            stopsTableBody.innerHTML = '';
            stops.forEach((stop, index) => {
                const row = document.createElement('tr');
                const stopNumber = (index === 0) ? "Start" : (index === stops.length - 1 && stops.length > 1) ? "End" : index;
                row.innerHTML = `
                    <td>${stopNumber}</td>
                    <td>${stop.locationName}</td>
                    <td>${stop.arrivalTime}</td>
                    <td>${stop.duration}</td>
                    <td>${stop.travelToNext ? stop.travelToNext.text : 'N/A'}</td>
                    <td>
                        <button class="edit-stop bg-blue-500 text-white px-2 py-1 rounded" data-index="${index}">Edit</button>
                        <button class="remove-stop bg-red-500 text-white px-2 py-1 rounded" data-index="${index}">Remove</button>
                        <button class="move-stop-up bg-gray-500 text-white p-2 rounded disabled:opacity-50" data-index="${index}" ${index === 0 ? 'disabled' : ''}><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 15-6-6-6 6"/></svg></button>
                        <button class="move-stop-down bg-gray-500 text-white p-2 rounded disabled:opacity-50" data-index="${index}" ${index === stops.length - 1 ? 'disabled' : ''}><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg></button>
                    </td>
                `;
                stopsTableBody.appendChild(row);
            });

            const removeButtons = document.querySelectorAll('.remove-stop');
            removeButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const buttonElement = e.target.closest('button');
                    const indexToRemove = parseInt(buttonElement.getAttribute('data-index'));
                    stops.splice(indexToRemove, 1);
                    resetTravelData();
                    renderStops();
                });
            });

            const moveUpButtons = document.querySelectorAll('.move-stop-up');
            moveUpButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const buttonElement = e.target.closest('button');
                    const indexToMove = parseInt(buttonElement.getAttribute('data-index'));
                    moveStopUp(indexToMove);
                });
            });

            const moveDownButtons = document.querySelectorAll('.move-stop-down');
            moveDownButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const buttonElement = e.target.closest('button');
                    const indexToMove = parseInt(buttonElement.getAttribute('data-index'));
                    moveStopDown(indexToMove);
                });
            });

            const editButtons = document.querySelectorAll('.edit-stop');
            editButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const buttonElement = e.target.closest('button');
                    const indexToEdit = parseInt(buttonElement.getAttribute('data-index'));
                    const stop = stops[indexToEdit];
                    document.getElementById('locationName').value = stop.locationName;
                    document.getElementById('arrivalTime').value = stop.arrivalTime;
                    document.getElementById('duration').value = stop.duration;
                    
                    // Change Add button to Save button
                    const addStopBtn = document.getElementById('addStopBtn');
                    addStopBtn.textContent = 'Save Stop';
                    const saveStopHandler = () => {
                        stops[indexToEdit] = {
                            locationName: document.getElementById('locationName').value,
                            arrivalTime: document.getElementById('arrivalTime').value,
                            duration: document.getElementById('duration').value
                        };
                        resetTravelData();
                        renderStops();
                        clearForm();
                        // Reset button
                        addStopBtn.textContent = 'Add Stop';
                        addStopBtn.removeEventListener('click', saveStopHandler);
                        addStopBtn.addEventListener('click', addStopHandler);
                    };
                    addStopBtn.removeEventListener('click', addStopHandler);
                    addStopBtn.addEventListener('click', saveStopHandler);
                });
            });
        }

        function clearForm() {
            document.getElementById('locationName').value = '';
            document.getElementById('arrivalTime').value = '';
            document.getElementById('duration').value = '';
        }

        function resetTravelData() {
            stops.forEach(stop => delete stop.travelToNext);
            travelSummary.innerHTML = '';
            document.getElementById('analysis-results').innerHTML = '';
            analyzeBtn.disabled = true;
            if (directionsRenderer) {
                directionsRenderer.setDirections({routes: []});
            }
        }

        function moveStopUp(index) {
            if (index > 0) {
                const stopToMove = stops.splice(index, 1)[0];
                stops.splice(index - 1, 0, stopToMove);
                resetTravelData();
                renderStops();
            }
        }

        function moveStopDown(index) {
            if (index < stops.length - 1) {
                const stopToMove = stops.splice(index, 1)[0];
                stops.splice(index + 1, 0, stopToMove);
                resetTravelData();
                renderStops();
            }
        }
        const clearTripBtn = document.getElementById('clearTripBtn');
        if (clearTripBtn) {
            clearTripBtn.addEventListener('click', () => {
                // Clear the stops array
                stops = [];
                
                // Clear all markers from the map
                tripMarkers.forEach(marker => marker.setMap(null));
                tripMarkers = [];

                // Clear the route from the map
                if (directionsRenderer) {
                    directionsRenderer.setDirections({ routes: [] });
                }

                // Reset the UI
                renderStops(); // This will clear the table
                travelSummary.innerHTML = '';
                document.getElementById('analysis-results').innerHTML = '';
                analyzeBtn.disabled = true;
                if (getDirectionsBtn) getDirectionsBtn.disabled = true;
                clearForm();
            });
        }
    }
    
    // --- NOTES SCREEN LOGIC ---
    const packingList = document.getElementById('packing-list');
    const packingListInput = document.getElementById('packing-list-input');
    const addPackingItemButton = document.getElementById('add-packing-item-button');
    const interestsTextarea = document.getElementById('interests-textarea');
    const documentFolderInput = document.getElementById('document-folder-input');
    const selectDocumentFolderButton = document.getElementById('select-document-folder-button');
    const documentListNotes = document.getElementById('document-list-notes');
    
    let packingItems = JSON.parse(localStorage.getItem('auraPackingList')) || [];
    
    function renderPackingList() {
        packingList.innerHTML = '';
        if (packingItems.length === 0) {
            packingList.innerHTML = '<p class="text-sm text-slate-500">Your packing list is empty.</p>';
            return;
        }
        packingItems.forEach((item, index) => {
            const itemEl = document.createElement('div');
            itemEl.className = 'flex items-center justify-between p-2 bg-slate-100 rounded-md';
            itemEl.innerHTML = `
                <div class="flex items-center">
                    <input type="checkbox" id="item-${index}" ${item.checked ? 'checked' : ''} class="h-4 w-4 text-sky-600 border-gray-300 rounded focus:ring-sky-500">
                    <label for="item-${index}" class="ml-3 text-slate-700 ${item.checked ? 'line-through' : ''}">${escapeHTML(item.text)}</label>
                </div>
                <button data-index="${index}" class="delete-packing-item text-slate-400 hover:text-red-500">&times;</button>
            `;
            packingList.appendChild(itemEl);
        });
    }
    
    function savePackingList() {
        localStorage.setItem('auraPackingList', JSON.stringify(packingItems));
    }
    
    addPackingItemButton.addEventListener('click', () => {
        const text = packingListInput.value.trim();
        if (text) {
            packingItems.push({ text, checked: false });
            packingListInput.value = '';
            savePackingList();
            renderPackingList();
        }
    });
    
    packingList.addEventListener('click', (e) => {
        if (e.target.type === 'checkbox') {
            const index = parseInt(e.target.id.split('-')[1]);
            packingItems[index].checked = e.target.checked;
            savePackingList();
            renderPackingList();
        }
        if (e.target.classList.contains('delete-packing-item')) {
            const index = parseInt(e.target.dataset.index);
            packingItems.splice(index, 1);
            savePackingList();
            renderPackingList();
        }
    });
    
    interestsTextarea.addEventListener('input', () => {
        localStorage.setItem('auraInterests', interestsTextarea.value);
    });
    
    selectDocumentFolderButton.addEventListener('click', () => {
        documentFolderInput.click();
    });
    
    documentFolderInput.addEventListener('change', (e) => {
        documentListNotes.innerHTML = '';
        if (e.target.files.length > 0) {
            const files = Array.from(e.target.files);
            files.forEach(file => {
                const fileEl = document.createElement('button');
                fileEl.className = 'w-full text-left p-2 bg-slate-100 rounded-md text-sm text-slate-700 hover:bg-slate-200';
                fileEl.textContent = file.name;
                fileEl.onclick = () => {
                    const singleFileInput = document.createElement('input');
                    singleFileInput.type = 'file';
                    singleFileInput.style.display = 'none';
                    // We can't pre-select the file, but we can open the file dialog.
                    // This is a browser security limitation.
                    singleFileInput.click();
                };
                documentListNotes.appendChild(fileEl);
            });
        }
    });
    
    // Load initial notes data
    interestsTextarea.value = localStorage.getItem('auraInterests') || '';
    renderPackingList();
    
});

closeSettingsButton.addEventListener('click', () => settingsModal.classList.add('hidden'));
settingsForm.addEventListener('submit', handleSaveSettings);

homeSosButton.addEventListener('click', handleSos);

cancelSos.addEventListener('click', () => sosOptionsModal.classList.add('hidden'));
alertContactButton.addEventListener('click', () => handleSmsAlert(false));
shareLocationButtonHome.addEventListener('click', handleHomeShareClick);
closeActionConfirmModal.addEventListener('click', () => actionConfirmModal.classList.add('hidden'));

let liveJourneyId = null;
let liveWatchId = null;

function handleHomeShareClick() {
    if (liveJourneyId) {
        stopLiveJourney();
    } else {
        startLiveJourney();
    }
}

async function startLiveJourney() {
    if (!userId) {
        console.error('User not authenticated');
        return;
    }
    
    const journeyId = generateSecureId();
    const journeyData = {
        id: journeyId,
        creatorId: userId,
        startTime: new Date().toISOString(),
        isActive: true,
        locations: []
    };
    
    try {
        await setDoc(doc(db, 'journeys', journeyId), journeyData);
        liveJourneyId = journeyId;
        
        // Start location tracking
        liveWatchId = navigator.geolocation.watchPosition(
            async (position) => {
                const location = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    timestamp: new Date().toISOString()
                };
                
                try {
                    await updateDoc(doc(db, 'journeys', journeyId), {
                        locations: arrayUnion(location)
                    });
                } catch (error) {
                    console.error('Error updating location:', error);
                }
            },
            (error) => {
                console.error('Geolocation error:', error);
                stopLiveJourney();
            },
            { enableHighAccuracy: true, maximumAge: 30000, timeout: 27000 }
        );
        
        const trackingUrl = `${window.location.origin}/journey.html?id=${journeyId}`;
        const statusElement = document.getElementById('sound-scape-status');
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(trackingUrl);
            alert('Live tracking link copied to clipboard. Share it with your contact.');
            statusElement.textContent = 'Live tracking link copied!';
        }

        shareLocationButtonHome.innerHTML = 'Stop<br>Sharing';
        statusElement.textContent = 'Live location sharing active';
        
    } catch (error) {
        console.error("Failed to start live journey:", error);
        let errorMessage = 'Could not start live sharing. ';
        if (error.code === 1) {
            errorMessage = 'Location permission denied. Please enable it in your browser settings.';
        }
        const statusElement = document.getElementById('sound-scape-status');
        statusElement.textContent = errorMessage;
        shareLocationButtonHome.innerHTML = 'Share My<br>Location';
        setTimeout(() => { statusElement.textContent = ''; }, 3000);
    }
}

async function stopLiveJourney() {
    if (liveWatchId) {
        navigator.geolocation.clearWatch(liveWatchId);
        liveWatchId = null;
    }
    if (liveJourneyId && db) {
        // Use the same path structure as in startLiveJourney
        const journeyDocRef = doc(db, 'journeys', liveJourneyId);
        try {
            await updateDoc(journeyDocRef, { active: false });
            console.log('Journey stopped successfully');
        } catch (error) {
            console.error('Error stopping journey:', error);
            // Don't throw error, just log it since the main goal is to stop local tracking
        }
        liveJourneyId = null;
    }
    shareLocationButtonHome.innerHTML = 'Share My<br>Location';
    const statusElement = document.getElementById('sound-scape-status');
    statusElement.textContent = 'Live sharing stopped.';
    setTimeout(() => { statusElement.textContent = ''; }, 3000);
}

stopJourneyButton.addEventListener('click', stopJourney);
copyJourneyLinkButton.addEventListener('click', copyJourneyLink);

messageForm.addEventListener('submit', handleMessageSubmit);
accessForm.addEventListener('submit', handleAccessCode);

fileInput.addEventListener('change', handleFileUpload);

// Listener for the new dedicated photo upload button
const photoUploadButton = document.getElementById('photo-upload-button');
if (photoUploadButton) {
    photoUploadButton.addEventListener('click', () => fileInput.click());
}

soundScapeButton.addEventListener('click', handleSoundScape);
navHome.addEventListener('click', () => showScreen('home'));
navSolo.addEventListener('click', () => {
    showScreen('solo');
    // The initMap callback will handle the map initialization,
    // but we call it here as well in case the user navigates
    // to the tab after the initial script load has already completed.
    initTripPlannerMap();
});
navGroup.addEventListener('click', () => {
    showScreen('group');
    // Default to South Australia and fetch directory
    regionSelect.value = 'South Australia';
    fetchAndRenderDirectory('South Australia');
    directoryList.classList.remove('hidden');
});
navInfo.addEventListener('click', () => showScreen('info'));
navCodex.addEventListener('click', () => {
    // Show the codex screen - the React component will handle journey selection
    showScreen('codex');

    // Check if there's already a journey parameter, if not the React component will auto-select
    const urlParams = new URLSearchParams(window.location.search);
    const existingJourney = urlParams.get('journey') || urlParams.get('codex');

    if (existingJourney) {
        // Trigger the React component to load the existing journey
        window.dispatchEvent(new CustomEvent('journey-load', { detail: { journeyId: existingJourney } }));
    }
    // If no journey exists, the React component will auto-select the first available journey
});

document.getElementById('info-sos-settings-button').addEventListener('click', () => settingsModal.classList.remove('hidden'));
document.getElementById('info-user-guide-button').addEventListener('click', () => showScreen('user-guide'));
document.getElementById('info-sponsors-button').addEventListener('click', () => showScreen('sponsors'));
document.getElementById('info-notes-button').addEventListener('click', () => showScreen('notes'));

document.getElementById('guide-back-to-info').addEventListener('click', () => showScreen('info'));
document.getElementById('sponsors-back-to-info').addEventListener('click', () => showScreen('info'));
document.getElementById('notes-back-to-info').addEventListener('click', () => showScreen('info'));


chatModeSwitcher.addEventListener('click', (e) => {
    const newMode = e.target.id;
    if (newMode === 'mode-photos') {
        currentViewMode = 'photos';
    } else {
        currentViewMode = 'chat';
    }
    updateUIForChatView();
    renderMessages(localMessages);
});

regionSelect.addEventListener('change', () => {
    const selectedRegion = regionSelect.value;
    if (selectedRegion) {
        fetchAndRenderDirectory(selectedRegion);
        directoryList.classList.remove('hidden');
    } else {
        directoryList.classList.add('hidden');
    }
});

async function fetchAndRenderDirectory(region) {
    directoryList.innerHTML = '<p class="text-center text-slate-500">Loading...</p>';
    
    try {
        await authReadyPromise;
        const directoryPath = `artifacts/${appId}/public/data/directory`;
        const directoryCollection = collection(db, directoryPath);
        const q = query(directoryCollection, where("regions", "array-contains", region));
        
        const querySnapshot = await getDocs(q);
        
        directoryList.innerHTML = '';
        if (querySnapshot.empty) {
            directoryList.innerHTML = '<p class="text-center text-slate-500">No activities found for this region.</p>';
            return;
        }
        
        querySnapshot.forEach((doc) => {
            const item = doc.data();
            const itemCard = document.createElement('div');
            itemCard.className = 'p-4 bg-white border rounded-lg shadow-sm cursor-pointer hover:bg-slate-50';
            // Join the regions array for display
            const regionText = item.regions ? item.regions.join(', ') : '';
            itemCard.innerHTML = `
                <div class="flex items-center gap-4">
                    <img src="${item.featured_image || 'https://placehold.co/64x64/a3e635/ffffff?text=A'}" class="w-16 h-16 rounded-md object-cover" onerror="this.onerror=null;this.src='https://placehold.co/64x64/a3e635/ffffff?text=A';">
                    <div>
                        <p class="font-bold text-slate-800">${item.name}</p>
                        <p class="text-sm text-slate-600">${regionText}</p>
                    </div>
                </div>
            `;
            itemCard.addEventListener('click', () => {
                // Handle directory item click
                console.log('Directory item clicked:', item.name);
                // Add navigation logic here
            });
            
            directoryList.appendChild(itemCard);
        });
        
    } catch (error) {
        console.error('Error fetching directory:', error);
        directoryList.innerHTML = '<p class="text-center text-slate-500">Error loading directory.</p>';
    }
}

function showDirectoryItemDetails(item) {
    directoryItemContent.innerHTML = `
        <img src="${item.featured_image || 'https://placehold.co/600x400/a3e635/ffffff?text=A'}" class="w-full h-48 object-cover rounded-t-lg" onerror="this.onerror=null;this.src='https://placehold.co/600x400/a3e635/ffffff?text=A';">
        <div class="p-6 text-center">
            <h2 class="text-2xl font-bold text-slate-800">${item.name}</h2>
            <p class="text-slate-600 mt-2 mb-4">${item.regions ? item.regions.join(', ') : ''}</p>
            <div class="text-slate-600 leading-relaxed">${item.description}</div>
        </div>
    `;
    directoryItemModal.classList.remove('hidden');
}

closeDirectoryModal.addEventListener('click', () => {
    directoryItemModal.classList.add('hidden');
});

function generateSecureId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input
        .replace(/[<>]/g, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+=/gi, '')
        .trim()
        .substring(0, 1000);
}