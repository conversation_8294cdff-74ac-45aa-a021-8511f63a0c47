Rotate Your API Key:


Create New Key in Google Cloud Console:
Go to Google Cloud Console > APIs & Services > Credentials
Click "Create Credentials" > "API Key"
Copy the new key

Update Your .env File:
GOOGLE_MAPS_API_KEY=your-new-api-key-here

Apply Same Restrictions:
Set domain restrictions
Enable only Maps JavaScript API
Set usage quotas

Delete Old Key:
Remove the old key from Google Cloud Console