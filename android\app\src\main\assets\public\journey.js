import { initializeApp, getApps, getApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
import { getFirestore, doc, onSnapshot } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

const firebaseConfig = {
  apiKey: "AIzaSyBA0X9xdiOL3nNdqzKibPIZVSciG88iR6Q",
  authDomain: "aura-app-backend.firebaseapp.com",
  projectId: "aura-app-backend",
  storageBucket: "aura-app-backend.firebasestorage.app",
  messagingSenderId: "905604520531",
  appId: "1:905604520531:web:bc77bae93a6a9ff7c104a6"
};

let map, marker, db, unsubscribe;
const statusText = document.getElementById('status-text');

// Dynamically load Google Maps API
function loadGoogleMaps() {
    return new Promise((resolve, reject) => {
        if (window.google && window.google.maps) {
            return resolve();
        }
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyDwwiFkI0LfJaYPuixc8fKo53pJITUQpMs&libraries=marker&v=weekly`;
        script.async = true;
        script.defer = true;
        script.onload = () => {
            // Wait for Google Maps to be fully loaded
            const checkGoogleMaps = () => {
                if (window.google && window.google.maps && window.google.maps.importLibrary) {
                    resolve();
                } else {
                    setTimeout(checkGoogleMaps, 100);
                }
            };
            checkGoogleMaps();
        };
        script.onerror = reject;
        document.head.appendChild(script);
    });
}


// Clean up the listener when the user navigates away
window.addEventListener('beforeunload', () => {
    if (unsubscribe) {
        unsubscribe();
    }
});

async function initMap() {
    try {
        await loadGoogleMaps();
        const urlParams = new URLSearchParams(window.location.search);
        const journeyId = urlParams.get('id');

        if (journeyId) {
            const { Map } = await google.maps.importLibrary("maps");
            const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");
            map = new Map(document.getElementById("map"), {
                center: { lat: -34.9285, lng: 138.6007 },
                zoom: 14,
                mapId: '9a6789cf3ff1289e'
            });
            marker = new AdvancedMarkerElement({
                map: map,
                title: "Live Location"
            });
            initializeAppAndListen();
        } else {
            document.getElementById("map").innerHTML = '<p class="text-center p-8">No journey specified.</p>';
            statusText.textContent = "No journey specified.";
        }
    } catch (error) {
        console.error("Failed to load Google Maps", error);
        statusText.textContent = "Could not load map.";
    }
}

function initializeAppAndListen() {
    try {
        const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();
        db = getFirestore(app);

        const urlParams = new URLSearchParams(window.location.search);
        const journeyId = urlParams.get('id');

        if (!journeyId) {
            statusText.textContent = "No journey ID provided.";
            return;
        }

        const journeyDocRef = doc(db, 'journeys', journeyId);

        unsubscribe = onSnapshot(journeyDocRef, (docSnap) => {
            if (docSnap.exists()) {
                const data = docSnap.data();
                if (data.active === false) {
                    statusText.textContent = "This journey has ended.";
                    if (unsubscribe) unsubscribe();
                    return;
                }

                if (data.latitude && data.longitude) {
                    const newPosition = { lat: data.latitude, lng: data.longitude };
                    if (marker) {
                        marker.position = newPosition;
                        map.setCenter(newPosition);
                    }
                    
                    // Update page title and add Google Maps link
                    document.title = "Live Location Tracking";
                    statusText.innerHTML = `
                        Location updated! 
                        <br>
                        <a href="https://www.google.com/maps?q=${data.latitude},${data.longitude}" 
                           target="_blank" 
                           class="text-blue-600 underline mt-2 inline-block">
                           Open in Google Maps
                        </a>
                    `;
                } else {
                    statusText.textContent = "Waiting for location data...";
                }
            } else {
                statusText.textContent = "This journey does not exist.";
                if (unsubscribe) unsubscribe();
            }
        }, (error) => {
            console.error("Error listening to journey:", error);
            statusText.textContent = "Error connecting to journey.";
        });

    } catch (error) {
        console.error("Initialization failed:", error);
        statusText.textContent = "Failed to initialize the app.";
    }
}

// Initialize the map on script load
initMap();


