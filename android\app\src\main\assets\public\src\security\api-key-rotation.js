/**
 * API Key Rotation and Management System
 * Provides automated API key rotation and health monitoring
 */

class ApiKeyRotationManager {
    constructor() {
        this.rotationInterval = 6 * 30 * 24 * 60 * 60 * 1000; // 6 months in milliseconds
        this.healthCheckInterval = 24 * 60 * 60 * 1000; // 24 hours
        this.lastHealthCheck = this.getLastHealthCheck();
        this.keyCreationDate = this.getKeyCreationDate();

        // If no creation date is stored, set it to now (fresh key)
        if (!this.keyCreationDate) {
            this.setKeyCreationDate();
        }

        this.initializeRotationSystem();
    }

    /**
     * Initialize the rotation monitoring system
     */
    initializeRotationSystem() {
        // Check if rotation is needed on app start
        this.checkRotationNeeded();
        
        // Set up periodic health checks
        this.setupHealthChecks();
        
        // Log initialization
        this.logRotationEvent('API Key Rotation Manager initialized', {
            keyAge: this.getKeyAge(),
            nextRotationDue: this.getNextRotationDate(),
            healthCheckStatus: this.isHealthCheckDue() ? 'due' : 'current'
        });
    }

    /**
     * Check if API key rotation is needed
     */
    checkRotationNeeded() {
        const keyAge = this.getKeyAge();
        const rotationDue = keyAge > this.rotationInterval;
        
        if (rotationDue) {
            this.notifyRotationNeeded();
        }
        
        return rotationDue;
    }

    /**
     * Get the age of the current API key
     */
    getKeyAge() {
        if (!this.keyCreationDate) {
            // If no creation date stored, assume key is old and needs rotation
            return this.rotationInterval + 1;
        }
        
        return Date.now() - this.keyCreationDate;
    }

    /**
     * Get key creation date from localStorage
     */
    getKeyCreationDate() {
        try {
            const stored = localStorage.getItem('mapsApiKeyCreationDate');
            return stored ? parseInt(stored) : null;
        } catch (error) {
            console.warn('Could not retrieve API key creation date:', error);
            return null;
        }
    }

    /**
     * Set key creation date (call this when rotating keys)
     */
    setKeyCreationDate(date = Date.now()) {
        try {
            localStorage.setItem('mapsApiKeyCreationDate', date.toString());
            this.keyCreationDate = date;
            this.logRotationEvent('API key creation date updated', { date: new Date(date).toISOString() });
        } catch (error) {
            console.warn('Could not store API key creation date:', error);
        }
    }

    /**
     * Get next rotation due date
     */
    getNextRotationDate() {
        if (!this.keyCreationDate) {
            return new Date(); // Rotation due now
        }
        
        return new Date(this.keyCreationDate + this.rotationInterval);
    }

    /**
     * Notify that rotation is needed
     */
    notifyRotationNeeded() {
        const message = `🔄 API Key Rotation Required: Your Google Maps API key is ${Math.floor(this.getKeyAge() / (24 * 60 * 60 * 1000))} days old and should be rotated for security.`;
        
        console.warn(message);
        
        this.logRotationEvent('API key rotation required', {
            keyAge: this.getKeyAge(),
            keyAgeDays: Math.floor(this.getKeyAge() / (24 * 60 * 60 * 1000)),
            severity: 'warning'
        });

        // Show user notification if in development
        if (window.APP_CONFIG?.environment === 'development') {
            this.showRotationNotification(message);
        }
    }

    /**
     * Show rotation notification to user
     */
    showRotationNotification(message) {
        // Only show notification if document.body exists
        if (!document.body) {
            console.warn('Cannot show rotation notification: document.body not available');
            return;
        }

        // Create a non-intrusive notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #fbbf24;
            color: #92400e;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 300px;
            font-size: 14px;
            font-family: system-ui, -apple-system, sans-serif;
        `;
        notification.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 4px;">🔄 Security Notice</div>
            <div>API key rotation recommended. Check console for details.</div>
            <button onclick="this.parentElement.remove()" style="
                position: absolute;
                top: 8px;
                right: 8px;
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
                color: #92400e;
            ">×</button>
        `;

        try {
            document.body.appendChild(notification);

            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 10000);
        } catch (error) {
            console.warn('Could not show rotation notification:', error);
        }
    }

    /**
     * Setup periodic health checks
     */
    setupHealthChecks() {
        // Check immediately if health check is due
        if (this.isHealthCheckDue()) {
            this.performHealthCheck();
        }
        
        // Set up periodic checks
        setInterval(() => {
            if (this.isHealthCheckDue()) {
                this.performHealthCheck();
            }
        }, 60 * 60 * 1000); // Check every hour
    }

    /**
     * Check if health check is due
     */
    isHealthCheckDue() {
        return !this.lastHealthCheck || (Date.now() - this.lastHealthCheck) > this.healthCheckInterval;
    }

    /**
     * Get last health check timestamp
     */
    getLastHealthCheck() {
        try {
            const stored = localStorage.getItem('mapsApiHealthCheck');
            return stored ? parseInt(stored) : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Perform API key health check
     */
    async performHealthCheck() {
        try {
            const apiKey = window.GOOGLE_MAPS_CONFIG?.apiKey;
            
            if (!apiKey || apiKey.includes('your-') || apiKey === 'loaded') {
                this.logRotationEvent('Health check skipped: API key not configured', {
                    severity: 'warning',
                    apiKey: apiKey ? apiKey.substring(0, 10) + '...' : 'undefined'
                });
                return false;
            }

            // Simple health check - try to load a minimal Maps API request
            const healthCheckUrl = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=core&callback=__mapsHealthCheck`;
            
            // Create a temporary script to test API key validity
            const script = document.createElement('script');
            script.src = healthCheckUrl;
            
            const healthCheckPromise = new Promise((resolve, reject) => {
                window.__mapsHealthCheck = () => {
                    resolve(true);
                    script.remove();
                    delete window.__mapsHealthCheck;
                };
                
                script.onerror = () => {
                    reject(new Error('API key health check failed'));
                    script.remove();
                    delete window.__mapsHealthCheck;
                };
                
                // Timeout after 10 seconds
                setTimeout(() => {
                    reject(new Error('Health check timeout'));
                    script.remove();
                    delete window.__mapsHealthCheck;
                }, 10000);
            });

            document.head.appendChild(script);
            
            await healthCheckPromise;
            
            // Update last health check timestamp
            localStorage.setItem('mapsApiHealthCheck', Date.now().toString());
            this.lastHealthCheck = Date.now();
            
            this.logRotationEvent('API key health check passed', { 
                timestamp: new Date().toISOString(),
                keyAge: this.getKeyAge()
            });
            
            return true;
            
        } catch (error) {
            // Check if this is just a configuration issue
            const apiKey = window.GOOGLE_MAPS_CONFIG?.apiKey;
            if (!apiKey || apiKey.includes('your-') || apiKey === 'loaded') {
                this.logRotationEvent('API key health check skipped: Not configured', {
                    error: 'API key not properly configured',
                    severity: 'info'
                });
                console.log('ℹ️ API Key Health Check Skipped: API key not configured');
            } else {
                this.logRotationEvent('API key health check failed', {
                    error: error.message,
                    severity: 'error'
                });
                console.error('🚨 API Key Health Check Failed:', error.message);
            }
            return false;
        }
    }

    /**
     * Log rotation events
     */
    logRotationEvent(event, details = {}) {
        const logEntry = {
            event: event,
            timestamp: new Date().toISOString(),
            keyAge: this.getKeyAge(),
            keyAgeDays: Math.floor(this.getKeyAge() / (24 * 60 * 60 * 1000)),
            ...details
        };

        // Log to console in development
        if (window.APP_CONFIG?.environment === 'development') {
            console.log('🔄 API Rotation Event:', logEntry);
        }

        // Store in localStorage for monitoring
        try {
            const events = JSON.parse(localStorage.getItem('apiRotationEvents') || '[]');
            events.push(logEntry);
            
            // Keep only last 30 events
            if (events.length > 30) {
                events.splice(0, events.length - 30);
            }
            
            localStorage.setItem('apiRotationEvents', JSON.stringify(events));
        } catch (error) {
            console.warn('Could not store rotation event:', error);
        }
    }

    /**
     * Get rotation statistics
     */
    getRotationStats() {
        return {
            keyAge: this.getKeyAge(),
            keyAgeDays: Math.floor(this.getKeyAge() / (24 * 60 * 60 * 1000)),
            rotationDue: this.checkRotationNeeded(),
            nextRotationDate: this.getNextRotationDate(),
            lastHealthCheck: this.lastHealthCheck ? new Date(this.lastHealthCheck) : null,
            healthCheckDue: this.isHealthCheckDue()
        };
    }

    /**
     * Manual rotation trigger (for testing or immediate rotation)
     */
    triggerRotation() {
        this.notifyRotationNeeded();
        this.logRotationEvent('Manual rotation triggered', { severity: 'info' });
    }
}

// Create global instance after DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.ApiKeyRotationManager = new ApiKeyRotationManager();
    });
} else {
    // DOM is already ready
    window.ApiKeyRotationManager = new ApiKeyRotationManager();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiKeyRotationManager;
}
