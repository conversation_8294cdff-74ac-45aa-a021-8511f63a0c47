const fs = require('fs');
const path = require('path');

// Directory containing the built files
const distDir = './dist';

// Files to fix
const htmlFiles = [
  'index.html',
  'journey.html',
  ];

console.log('🔧 Fixing CSS paths in HTML files...');

htmlFiles.forEach(fileName => {
  const filePath = path.join(distDir, fileName);
  
  if (fs.existsSync(filePath)) {
    console.log(`Processing ${fileName}...`);
    
    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix the paths - be very specific with replacements
    content = content.replace(/href="\.\/dist\/tailwind\.css"/g, 'href="./tailwind.css"');
    content = content.replace(/href="\.\/dist\/style\.css"/g, 'href="./style.css"');
    content = content.replace(/href="\.\/dist\/solo\.css"/g, 'href="./solo.css"');

    // Fix config.js path
    content = content.replace(/src="\.\/dist\/config\.js"/g, 'src="./config.js"');

    // Fix security script paths
    content = content.replace(/src="\.\/src\/security\/maps-security\.js"/g, 'src="./maps-security.js"');
    content = content.replace(/src="\.\/src\/security\/api-key-rotation\.js"/g, 'src="./api-key-rotation.js"');
    content = content.replace(/src="\.\/src\/security\/xss-protection\.js"/g, 'src="./xss-protection.js"');
    content = content.replace(/src="\.\/src\/security\/error-handler\.js"/g, 'src="./error-handler.js"');
    content = content.replace(/src="\.\/src\/security\/csp-config\.js"/g, 'src="./csp-config.js"');

    // Fix debug paths
    content = content.replace(/\/dist\/codex\.js/g, '/codex.js');
    content = content.replace(/\.\/dist\/codex\.js/g, './codex.js');

    // Also fix any src/ references
    content = content.replace(/href="\.\/src\/style\.css"/g, 'href="./style.css"');
    content = content.replace(/href="\.\/src\/solo\.css"/g, 'href="./solo.css"');
    
    // Replace CDN with local file if needed
    content = content.replace(
      /<script src="https:\/\/cdn\.tailwindcss\.com"><\/script>/g,
      '<link rel="stylesheet" href="./tailwind.css">'
    );
    
    // Write the fixed content back
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed paths in ${fileName}`);
  } else {
    console.log(`⚠️ File not found: ${filePath}`);
  }
});

console.log('✅ All paths fixed!');