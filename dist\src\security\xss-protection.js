/**
 * XSS Protection Module
 * Provides comprehensive protection against cross-site scripting attacks
 */

class XSSProtection {
    constructor() {
        this.allowedTags = ['b', 'i', 'em', 'strong', 'br'];
        this.allowedAttributes = {
            'a': ['href', 'target', 'rel'],
            'img': ['src', 'alt', 'width', 'height']
        };
        this.urlPattern = /^https?:\/\/[^\s<>"']+$/i;
        this.initializeProtection();
    }

    /**
     * Initialize XSS protection system
     */
    initializeProtection() {
        // Override dangerous methods
        this.setupInnerHTMLProtection();
        
        // Log initialization
        this.logXSSEvent('XSS Protection initialized', {
            allowedTags: this.allowedTags,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Enhanced HTML escaping with additional security
     */
    escapeHTML(str) {
        if (!str) return '';
        
        // Convert to string if not already
        str = String(str);
        
        // Basic HTML entity escaping
        const entityMap = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;',
            '/': '&#x2F;',
            '`': '&#x60;',
            '=': '&#x3D;'
        };
        
        return str.replace(/[&<>"'`=\/]/g, (s) => entityMap[s]);
    }

    /**
     * Sanitize HTML content allowing only safe tags
     */
    sanitizeHTML(html) {
        if (!html) return '';
        
        // Create a temporary DOM element for parsing
        const temp = document.createElement('div');
        temp.innerHTML = html;
        
        // Recursively clean the content
        this.cleanElement(temp);
        
        return temp.innerHTML;
    }

    /**
     * Clean DOM element recursively
     */
    cleanElement(element) {
        const children = Array.from(element.childNodes);
        
        children.forEach(child => {
            if (child.nodeType === Node.TEXT_NODE) {
                // Text nodes are safe, keep as-is
                return;
            }
            
            if (child.nodeType === Node.ELEMENT_NODE) {
                const tagName = child.tagName.toLowerCase();
                
                // Remove disallowed tags
                if (!this.allowedTags.includes(tagName)) {
                    // Replace with text content
                    const textNode = document.createTextNode(child.textContent);
                    element.replaceChild(textNode, child);
                    return;
                }
                
                // Clean attributes
                this.cleanAttributes(child);
                
                // Recursively clean children
                this.cleanElement(child);
            } else {
                // Remove other node types (comments, etc.)
                element.removeChild(child);
            }
        });
    }

    /**
     * Clean element attributes
     */
    cleanAttributes(element) {
        const tagName = element.tagName.toLowerCase();
        const allowedAttrs = this.allowedAttributes[tagName] || [];
        const attributes = Array.from(element.attributes);
        
        attributes.forEach(attr => {
            if (!allowedAttrs.includes(attr.name.toLowerCase())) {
                element.removeAttribute(attr.name);
            } else {
                // Validate attribute values
                this.validateAttributeValue(element, attr.name, attr.value);
            }
        });
    }

    /**
     * Validate attribute values
     */
    validateAttributeValue(element, attrName, attrValue) {
        const tagName = element.tagName.toLowerCase();
        
        // Validate URLs
        if ((attrName === 'href' || attrName === 'src') && attrValue) {
            if (!this.isValidURL(attrValue)) {
                element.removeAttribute(attrName);
                this.logXSSEvent('Invalid URL removed', {
                    tag: tagName,
                    attribute: attrName,
                    value: attrValue.substring(0, 100) // Truncate for logging
                });
            }
        }
        
        // Ensure target="_blank" has rel="noopener noreferrer"
        if (attrName === 'target' && attrValue === '_blank') {
            element.setAttribute('rel', 'noopener noreferrer');
        }
    }

    /**
     * Validate URL safety
     */
    isValidURL(url) {
        if (!url) return false;
        
        // Check for javascript: protocol and other dangerous schemes
        const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:', 'ftp:'];
        const lowerUrl = url.toLowerCase().trim();
        
        for (const protocol of dangerousProtocols) {
            if (lowerUrl.startsWith(protocol)) {
                return false;
            }
        }
        
        // Allow relative URLs and safe absolute URLs
        return url.startsWith('/') || url.startsWith('./') || url.startsWith('../') || this.urlPattern.test(url);
    }

    /**
     * Secure message rendering for chat
     */
    renderSecureMessage(messageData) {
        const {
            text = '',
            senderName = '',
            fileName = '',
            fileURL = '',
            type = 'text',
            isCurrentUser = false
        } = messageData;

        // Escape all user-provided content
        const safeText = this.escapeHTML(text);
        const safeSenderName = this.escapeHTML(senderName);
        const safeFileName = this.escapeHTML(fileName);
        
        // Validate and sanitize URLs
        const safeFileURL = this.isValidURL(fileURL) ? fileURL : '#';
        
        if (safeFileURL === '#' && fileURL) {
            this.logXSSEvent('Unsafe URL blocked in message', {
                originalURL: fileURL.substring(0, 100),
                type: type
            });
        }

        // Build safe HTML based on message type
        let contentHtml = '';
        
        if (type === 'file') {
            contentHtml = `
                <a href="${safeFileURL}" target="_blank" rel="noopener noreferrer" 
                   class="flex items-center gap-2 p-3 rounded-lg ${isCurrentUser ? 'bg-sky-600' : 'bg-slate-300'}">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                        <polyline points="14 2 14 8 20 8"/>
                        <line x1="16" y1="13" x2="8" y2="13"/>
                        <line x1="16" y1="17" x2="8" y2="17"/>
                        <line x1="10" y1="9" x2="8" y2="9"/>
                    </svg>
                    <span>${safeFileName}</span>
                </a>
            `;
        } else {
            contentHtml = `<p class="text-sm">${safeText || 'No message content'}</p>`;
        }

        return {
            senderName: safeSenderName,
            contentHtml: contentHtml
        };
    }

    /**
     * Secure SOS message rendering
     */
    renderSecureSOS(sosData) {
        const {
            senderRole = '',
            location = { latitude: 0, longitude: 0 }
        } = sosData;

        const safeSenderRole = this.escapeHTML(senderRole);
        
        // Validate coordinates
        const lat = parseFloat(location.latitude) || 0;
        const lng = parseFloat(location.longitude) || 0;
        
        // Ensure coordinates are within valid ranges
        const validLat = Math.max(-90, Math.min(90, lat));
        const validLng = Math.max(-180, Math.min(180, lng));
        
        const mapsURL = `https://www.google.com/maps?q=${validLat},${validLng}`;

        return `
            <p class="font-bold text-lg">🆘 SOS ALERT 🆘</p>
            <p class="text-sm">Sent by ${safeSenderRole}.</p>
            <p class="text-sm mt-1">Location: 
                <a href="${mapsURL}" target="_blank" rel="noopener noreferrer" class="underline hover:text-red-600">
                    View on Map
                </a>
            </p>
        `;
    }

    /**
     * Setup innerHTML protection (development warning)
     */
    setupInnerHTMLProtection() {
        // Disabled for now to prevent conflicts with other libraries
        // TODO: Implement safer monitoring approach
        if (false && window.APP_CONFIG?.environment === 'development') {
            // In development, warn about direct innerHTML usage
            const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
            const xssProtection = this; // Capture the correct context

            Object.defineProperty(Element.prototype, 'innerHTML', {
                set: function(value) {
                    // Check if the value contains potential XSS
                    if (typeof value === 'string' && xssProtection.hasXSSRisk(value)) {
                        console.warn('⚠️ Potential XSS risk detected in innerHTML assignment:', {
                            element: this.tagName,
                            value: value.substring(0, 100)
                        });
                    }

                    return originalInnerHTML.set.call(this, value);
                },
                get: originalInnerHTML.get
            });
        }
    }

    /**
     * Check if content has XSS risk
     */
    hasXSSRisk(content) {
        const xssPatterns = [
            /<script/i,
            /javascript:/i,
            /on\w+\s*=/i,
            /<iframe/i,
            /<object/i,
            /<embed/i,
            /data:text\/html/i
        ];
        
        return xssPatterns.some(pattern => pattern.test(content));
    }

    /**
     * Safe DOM manipulation helpers
     */
    safeSetTextContent(element, text) {
        if (element && typeof text === 'string') {
            element.textContent = text;
        }
    }

    safeSetHTML(element, html) {
        if (element && typeof html === 'string') {
            element.innerHTML = this.sanitizeHTML(html);
        }
    }

    safeAppendHTML(element, html) {
        if (element && typeof html === 'string') {
            const temp = document.createElement('div');
            temp.innerHTML = this.sanitizeHTML(html);
            
            while (temp.firstChild) {
                element.appendChild(temp.firstChild);
            }
        }
    }

    /**
     * Log XSS events
     */
    logXSSEvent(event, details = {}) {
        const logEntry = {
            event: event,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent.substring(0, 100),
            url: window.location.href,
            ...details
        };

        // Log to console in development
        if (window.APP_CONFIG?.environment === 'development') {
            console.log('🛡️ XSS Protection Event:', logEntry);
        }

        // Store in localStorage for monitoring
        try {
            const events = JSON.parse(localStorage.getItem('xssProtectionEvents') || '[]');
            events.push(logEntry);
            
            // Keep only last 50 events
            if (events.length > 50) {
                events.splice(0, events.length - 50);
            }
            
            localStorage.setItem('xssProtectionEvents', JSON.stringify(events));
        } catch (error) {
            console.warn('Could not store XSS protection event:', error);
        }
    }

    /**
     * Get XSS protection statistics
     */
    getProtectionStats() {
        try {
            const events = JSON.parse(localStorage.getItem('xssProtectionEvents') || '[]');
            const recentEvents = events.filter(event => 
                Date.now() - new Date(event.timestamp).getTime() < 24 * 60 * 60 * 1000
            );
            
            return {
                totalEvents: events.length,
                recentEvents: recentEvents.length,
                lastEvent: events.length > 0 ? events[events.length - 1] : null,
                protectionActive: true
            };
        } catch (error) {
            return {
                totalEvents: 0,
                recentEvents: 0,
                lastEvent: null,
                protectionActive: true
            };
        }
    }
}

// Create global instance
window.XSSProtection = new XSSProtection();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = XSSProtection;
}
