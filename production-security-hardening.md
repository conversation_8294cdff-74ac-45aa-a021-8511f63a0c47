# 🛡️ Production Security Hardening Guide - Monoloci App

## 🚨 IMMEDIATE ACTIONS REQUIRED (Before Production)

### 1. Fix Critical Vulnerabilities
```bash
# Update vulnerable dependencies
npm audit fix
npm update firebase@latest
npm update react@latest react-dom@latest

# Remove hardcoded API keys
# Edit app-changed-auth.js to remove hardcoded Firebase config
# Ensure all API keys use environment variables
```

### 2. Secure API Keys
```bash
# Google Cloud Console - API Key Restrictions
1. Go to Google Cloud Console > APIs & Services > Credentials
2. Find your Firebase API key
3. Click "Restrict key"
4. Set Application restrictions:
   - HTTP referrers (web sites)
   - Add your domain: https://yourdomain.com/*
5. Set API restrictions:
   - Select specific APIs only
   - Enable: Firebase, Cloud Firestore, Maps JavaScript API
```

## 🔒 INFRASTRUCTURE SECURITY

### HTTP Security Headers
Add these headers to your hosting configuration:

```nginx
# For Nginx
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Permissions-Policy "geolocation=(self), microphone=(self), camera=(self)" always;
```

```apache
# For Apache
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-Content-Type-Options "nosniff"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Permissions-Policy "geolocation=(self), microphone=(self), camera=(self)"
```

### Content Security Policy (CSP)
Update your CSP configuration:

```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://apis.google.com https://www.gstatic.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https: blob:;
  connect-src 'self' https://*.googleapis.com https://*.firebaseio.com wss://*.firebaseio.com;
  frame-src 'none';
  object-src 'none';
  base-uri 'self';
  form-action 'self';
">
```

## 🔥 FIREBASE SECURITY HARDENING

### Enhanced Firestore Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Enhanced user data protection
    match /users/{userId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId
        && isValidUserData(request.resource.data);
      
      // Rate limiting for sensitive operations
      match /rateLimiting/{document} {
        allow write: if request.auth != null 
          && request.auth.uid == userId
          && isWithinRateLimit();
      }
    }
    
    // Enhanced journey security
    match /journeys/{journeyId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null 
        && (resource == null || resource.data.createdBy == request.auth.uid)
        && isValidJourneyData(request.resource.data);
    }
    
    // Functions for validation
    function isValidUserData(data) {
      return data.keys().hasAll(['name', 'email']) 
        && data.name is string 
        && data.name.size() <= 100
        && data.email is string 
        && data.email.matches('.*@.*\\..*');
    }
    
    function isValidJourneyData(data) {
      return data.keys().hasAll(['title', 'createdBy', 'createdAt'])
        && data.title is string 
        && data.title.size() <= 200;
    }
    
    function isWithinRateLimit() {
      return request.time > resource.data.timestamp + duration.value(1, 'm');
    }
  }
}
```

### Firebase Storage Rules
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId
        && isValidFile();
    }
    
    function isValidFile() {
      return resource.size < 50 * 1024 * 1024 // 50MB limit
        && resource.contentType.matches('image/.*|application/pdf|audio/.*|text/.*');
    }
  }
}
```

## 🔐 APPLICATION SECURITY

### Input Validation Enhancement
```javascript
// Enhanced validation functions
function validateAndSanitizeInput(input, maxLength = 500) {
    if (!input || typeof input !== 'string') return '';
    
    // Remove dangerous patterns
    const sanitized = input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .replace(/[<>]/g, '')
        .trim()
        .substring(0, maxLength);
    
    return sanitized;
}

function validateFileUpload(file) {
    const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'text/plain',
        'audio/mpeg', 'audio/wav', 'audio/ogg'
    ];
    
    const maxSizes = {
        'image/': 10 * 1024 * 1024, // 10MB
        'application/pdf': 25 * 1024 * 1024, // 25MB
        'audio/': 50 * 1024 * 1024, // 50MB
        'text/': 1 * 1024 * 1024 // 1MB
    };
    
    if (!allowedTypes.includes(file.type)) {
        throw new Error('File type not allowed');
    }
    
    const maxSize = Object.keys(maxSizes).find(type => file.type.startsWith(type));
    if (file.size > maxSizes[maxSize]) {
        throw new Error('File size exceeds limit');
    }
    
    return true;
}
```

### Error Handling Security
```javascript
// Secure error handling
function handleSecureError(error, context) {
    // Log detailed error for debugging (server-side only)
    console.error(`[${context}] Error:`, error);
    
    // Return generic error to client
    const userMessage = {
        'auth': 'Authentication failed. Please try again.',
        'network': 'Network error. Please check your connection.',
        'validation': 'Invalid input. Please check your data.',
        'default': 'An error occurred. Please try again.'
    };
    
    return userMessage[context] || userMessage.default;
}
```

## 📊 MONITORING & ALERTING

### Security Event Monitoring
```javascript
// Security event logging
function logSecurityEvent(eventType, details) {
    const securityEvent = {
        timestamp: new Date().toISOString(),
        type: eventType,
        userAgent: navigator.userAgent,
        ip: 'server-side-only', // Get from server
        details: details,
        severity: getSeverityLevel(eventType)
    };
    
    // Send to security monitoring service
    if (window.SecurityMonitor) {
        window.SecurityMonitor.logEvent(securityEvent);
    }
}

function getSeverityLevel(eventType) {
    const severityMap = {
        'xss_attempt': 'high',
        'auth_failure': 'medium',
        'rate_limit_exceeded': 'medium',
        'invalid_input': 'low'
    };
    
    return severityMap[eventType] || 'low';
}
```

### Performance & Security Monitoring
```javascript
// Monitor for potential attacks
function monitorSecurityMetrics() {
    // Track error rates
    const errorRate = getErrorRate();
    if (errorRate > 0.1) { // 10% error rate threshold
        logSecurityEvent('high_error_rate', { rate: errorRate });
    }
    
    // Track API usage
    const apiUsage = getAPIUsage();
    if (apiUsage.requestsPerMinute > 100) {
        logSecurityEvent('high_api_usage', apiUsage);
    }
    
    // Track authentication failures
    const authFailures = getAuthFailures();
    if (authFailures > 5) {
        logSecurityEvent('multiple_auth_failures', { count: authFailures });
    }
}

// Run monitoring every minute
setInterval(monitorSecurityMetrics, 60000);
```

## 🚀 DEPLOYMENT CHECKLIST

### Pre-Deployment Security Verification
- [ ] All critical and high-priority vulnerabilities resolved
- [ ] Dependencies updated to latest secure versions
- [ ] API keys properly restricted in Google Cloud Console
- [ ] Firebase security rules reviewed and hardened
- [ ] Security headers configured on hosting platform
- [ ] CSP policy implemented and tested
- [ ] HTTPS enforced with valid SSL certificate
- [ ] Error handling sanitized for production
- [ ] Security monitoring and alerting configured
- [ ] Backup and recovery procedures documented

### Post-Deployment Monitoring
- [ ] Security dashboard monitoring active
- [ ] Error rate alerts configured
- [ ] API usage monitoring enabled
- [ ] Authentication failure tracking active
- [ ] Regular security scans scheduled
- [ ] Incident response plan documented
- [ ] Security team contact information updated

## 📞 INCIDENT RESPONSE

### Security Incident Response Plan
1. **Detection**: Automated alerts trigger incident response
2. **Assessment**: Evaluate severity and impact within 15 minutes
3. **Containment**: Implement immediate protective measures
4. **Investigation**: Analyze attack vectors and affected systems
5. **Recovery**: Restore services and implement fixes
6. **Lessons Learned**: Document and improve security measures

### Emergency Contacts
- Security Team: [<EMAIL>]
- DevOps Team: [<EMAIL>]
- Management: [<EMAIL>]

---

**⚠️ CRITICAL**: Complete all items in this hardening guide before production deployment. Regular security reviews should be conducted monthly.
