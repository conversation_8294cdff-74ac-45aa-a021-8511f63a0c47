const fs = require('fs');
const path = require('path');
const { generateConfigFile } = require('./config.js');

// Simple build script for the Monoloci app
console.log('🔨 Building Monoloci App for deployment...');

// Generate secure configuration first
console.log('🔧 Generating secure configuration...');
try {
    generateConfigFile();
} catch (error) {
    console.error('❌ Failed to generate configuration:', error.message);
    console.log('⚠️  Continuing with build, but configuration may be incomplete');
}

// Create dist directory
const distDir = './dist';
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir);
}

// Files to copy to dist
const filesToCopy = [
    'index.html',
    'app.js',
    'manifest.json',
    'sw.js',
    'journey.html',
    'journey.js',
    'codex.js',
];

// Create src directory structure in dist
const srcDir = path.join(distDir, 'src');
const securityDir = path.join(srcDir, 'security');
const utilsDir = path.join(srcDir, 'utils');
if (!fs.existsSync(securityDir)) {
    fs.mkdirSync(securityDir, { recursive: true });
}
if (!fs.existsSync(utilsDir)) {
    fs.mkdirSync(utilsDir, { recursive: true });
}

// Copy Firebase native integration file
if (fs.existsSync('src/utils/firebase-native.js')) {
    fs.copyFileSync('src/utils/firebase-native.js', path.join(utilsDir, 'firebase-native.js'));
    console.log('✅ Copied firebase-native.js');
}

// Copy security files to both locations for compatibility
if (fs.existsSync('src/security/maps-security.js')) {
    fs.copyFileSync('src/security/maps-security.js', path.join(securityDir, 'maps-security.js'));
    fs.copyFileSync('src/security/maps-security.js', path.join(distDir, 'maps-security.js'));
    console.log('✅ Copied maps-security.js');
}

if (fs.existsSync('src/security/api-key-rotation.js')) {
    fs.copyFileSync('src/security/api-key-rotation.js', path.join(securityDir, 'api-key-rotation.js'));
    fs.copyFileSync('src/security/api-key-rotation.js', path.join(distDir, 'api-key-rotation.js'));
    console.log('✅ Copied api-key-rotation.js');
}

if (fs.existsSync('src/security/xss-protection.js')) {
    fs.copyFileSync('src/security/xss-protection.js', path.join(securityDir, 'xss-protection.js'));
    fs.copyFileSync('src/security/xss-protection.js', path.join(distDir, 'xss-protection.js'));
    console.log('✅ Copied xss-protection.js');
}

if (fs.existsSync('src/security/csp-config.js')) {
    fs.copyFileSync('src/security/csp-config.js', path.join(securityDir, 'csp-config.js'));
    fs.copyFileSync('src/security/csp-config.js', path.join(distDir, 'csp-config.js'));
    console.log('✅ Copied csp-config.js');
}

// Copy files to dist
filesToCopy.forEach(file => {
    if (fs.existsSync(file)) {
        fs.copyFileSync(file, path.join(distDir, file));
        console.log(`✅ Copied ${file}`);
    } else {
        console.log(`⚠️  Warning: ${file} not found in root directory`);
    }
});

// CSS files to copy from src directory
const cssFilesToCopy = [
    'src/style.css',
    'src/solo.css'
];

// Copy CSS files to dist
fs.copyFileSync('src/style.css', 'dist/style.css');
fs.copyFileSync('src/solo.css', 'dist/solo.css');
console.log('✅ Copied CSS files');

// Generate Tailwind CSS for production using PostCSS
console.log('🎨 Generating Tailwind CSS with PostCSS...');
const { execSync } = require('child_process');
try {
    // Use PostCSS with the production config
    execSync('npx postcss src/input.css --config postcss.prod.config.js -o dist/tailwind.css', { stdio: 'inherit' });
    console.log('✅ Generated tailwind.css with PostCSS');
} catch (error) {
    console.log('⚠️  Warning: Could not generate tailwind.css with PostCSS, trying fallback');
    try {
        // Fallback to regular postcss config
        execSync('npx postcss src/input.css -o dist/tailwind.css', { stdio: 'inherit' });
        console.log('✅ Generated tailwind.css with default PostCSS config');
    } catch (fallbackError) {
        console.log('⚠️  Warning: PostCSS failed, creating minimal fallback');
        const fallbackCSS = '@import url("https://cdn.tailwindcss.com");';
        fs.writeFileSync('dist/tailwind.css', fallbackCSS);
    }
}

// --- Path Correction Step ---
console.log('🔧 Updating CSS paths in HTML files for dist...');
const htmlFilesToCorrect = ['index.html', 'journey.html'];
htmlFilesToCorrect.forEach(fileName => {
    const filePath = path.join(distDir, fileName);
    if (fs.existsSync(filePath)) {
        let fileContent = fs.readFileSync(filePath, 'utf8');
        
        // Replace CDN with built CSS
        fileContent = fileContent.replace(
            /<script src="https:\/\/cdn\.tailwindcss\.com"><\/script>/g, 
            '<link rel="stylesheet" href="./tailwind.css">'
        );
        
        // Update CSS paths for dist deployment - fix these paths!
        fileContent = fileContent.replace(/href="\.\/dist\/style\.css"/g, 'href="./style.css"');
        fileContent = fileContent.replace(/href="\.\/dist\/solo\.css"/g, 'href="./solo.css"');
        fileContent = fileContent.replace(/href="\.\/src\/style\.css"/g, 'href="./style.css"');
        fileContent = fileContent.replace(/href="\.\/src\/solo\.css"/g, 'href="./solo.css"');
        
        fs.writeFileSync(filePath, fileContent, 'utf8');
        console.log(`✅ Updated CSS paths in ${fileName}`);
    }
});

// Copy codex.js to root directory for direct loading
if (fs.existsSync(path.join(distDir, 'codex.js'))) {
    fs.copyFileSync(path.join(distDir, 'codex.js'), 'codex.js');
    console.log('✅ Copied codex.js to root directory');
} else {
    console.log('⚠️  Warning: dist/codex.js not found');
}

// Create icons directory in dist
const iconsDir = path.join(distDir, 'icons');
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir);
}

// Copy actual icons from source icons directory
const sourceIconsDir = './icons';
if (fs.existsSync(sourceIconsDir)) {
    const iconFiles = fs.readdirSync(sourceIconsDir);
    iconFiles.forEach(iconFile => {
        if (iconFile.endsWith('.png') || iconFile.endsWith('.svg')) {
            const sourcePath = path.join(sourceIconsDir, iconFile);
            const destPath = path.join(iconsDir, iconFile);
            fs.copyFileSync(sourcePath, destPath);
            console.log(`✅ Copied icon: ${iconFile}`);
        }
    });

    // Create specific named icons for PWA compatibility
    if (fs.existsSync(path.join(sourceIconsDir, 'icon-192.png'))) {
        fs.copyFileSync(path.join(sourceIconsDir, 'icon-192.png'), path.join(iconsDir, 'icon-192.png'));
    }
    if (fs.existsSync(path.join(sourceIconsDir, 'icon-512.png'))) {
        fs.copyFileSync(path.join(sourceIconsDir, 'icon-512.png'), path.join(iconsDir, 'icon-512.png'));
    }

    console.log('✅ Copied actual icons from source');
} else {
    // Fallback: Create placeholder icons if source directory doesn't exist
    const iconSizes = [16, 32, 72, 96, 128, 144, 152, 192, 384, 512];
    iconSizes.forEach(size => {
        const iconContent = createPlaceholderIcon(size);
        fs.writeFileSync(path.join(iconsDir, `icon-${size}x${size}.png`), iconContent);
    });

    // Create apple-touch-icon
    fs.writeFileSync(path.join(iconsDir, 'apple-touch-icon.png'), createPlaceholderIcon(180));

    console.log('⚠️  Source icons not found, created placeholder icons');
}

// Create a simple deployment guide
const deploymentGuide = `# 🚀 Deployment Guide for Monoloci App

## 📦 What's in the dist/ folder?

Your app has been built and optimized for deployment. The \`dist/\` folder contains:
- Minified and bundled JavaScript files
- Optimized CSS with Tailwind
- All necessary HTML files
- PWA manifest and service worker
- Icons and assets

## 🌐 Deployment Options

### Option 1: Static Hosting Services

#### GitHub Pages
1. Push the \`dist/\` folder contents to a GitHub repository
2. Enable GitHub Pages in repository settings
3. Your app will be available at \`https://username.github.io/repository-name\`

#### Firebase Hosting
1. Install Firebase CLI: \`npm install -g firebase-tools\`
2. Run \`firebase init hosting\` in your project directory
3. Set public directory to \`dist\`
4. Run \`firebase deploy\`

### Option 2: Traditional Web Hosting
1. Upload the contents of the \`dist/\` folder to your web server
2. Ensure your server supports HTTPS (required for PWA features)
3. Configure your server to serve the manifest.json with correct MIME type

## 📱 Mobile App Creation

### Option 1: PWA (Progressive Web App) - Recommended
- Your app is already PWA-ready!
- Users can install it directly from their browser
- Works on both Android and iOS
- No app store approval needed

### Option 2: Capacitor (Native App Wrapper)
\`\`\`bash
# Install Capacitor
npm install @capacitor/core @capacitor/cli
npm install @capacitor/android @capacitor/ios

# Initialize Capacitor
npx cap init "Monoloci" "com.Monoloci.companion"

# Add platforms
npx cap add android
npx cap add ios

# Copy web assets
npx cap copy

# Open in native IDEs
npx cap open android
npx cap open ios
\`\`\`

## 🔧 Configuration Before Deployment

### 1. Update Firebase Configuration
- Ensure your Firebase project is properly configured
- Update security rules for production
- Set up proper authentication

### 2. Update Domain References
- Replace localhost references with your actual domain
- Update any hardcoded URLs in the code

### 3. Icons and Branding
- Replace placeholder icons with your actual app icons
- Update app name and branding in manifest.json

### 4. Security
- Ensure HTTPS is enabled (required for PWA)
- Review and update Content Security Policy if needed
- Test all features in production environment

## 📊 Testing Your Deployment

1. **PWA Features**: Use Chrome DevTools > Application > Manifest
2. **Service Worker**: Check if it's registered and working
3. **Offline Functionality**: Test with network disabled
4. **Mobile Install**: Test "Add to Home Screen" on mobile devices
5. **Firebase Connection**: Verify data sync is working

## 🎯 Performance Optimization

- Enable gzip compression on your server
- Set up proper caching headers
- Consider using a CDN for static assets
- Optimize images and icons

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Verify Firebase configuration
3. Test PWA features using Chrome DevTools
4. Ensure HTTPS is properly configured

Your app is now ready for the world! 🌍
`;

fs.writeFileSync(path.join(distDir, 'DEPLOYMENT.md'), deploymentGuide);

console.log('📋 Created deployment guide');
console.log('🎉 Build complete! Check the dist/ folder for deployable files.');
console.log('📖 Read dist/DEPLOYMENT.md for deployment instructions.');

// Simple function to create placeholder icon (base64 encoded 1x1 PNG)
function createPlaceholderIcon(size) {
    // This is a minimal PNG file - you should replace with actual icons
    return Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
}
